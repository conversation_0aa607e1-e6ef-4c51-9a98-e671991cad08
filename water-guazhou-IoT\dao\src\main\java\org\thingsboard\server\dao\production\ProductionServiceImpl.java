package org.thingsboard.server.dao.production;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.client.OperatingIncomeInputFeign;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.VO.*;
import org.thingsboard.server.dao.model.sql.OperatingIncomeInput;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.sql.alarm.AlarmCountRepository;
import org.thingsboard.server.dao.stationData.StationDataService;
import org.thingsboard.server.dao.util.DeviceTableInfoUtil;
import org.thingsboard.server.dao.util.StationDataUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 智慧生产通用服务层实现
 */
@Slf4j
@Service
public class ProductionServiceImpl implements ProductionService {

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    @Autowired
    private OperatingIncomeInputFeign operatingIncomeInputFeign;

    @Autowired
    protected DeviceService deviceService;

    @Autowired
    private AlarmCountRepository alarmCountRepository;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // 是否启用缓存
    @Value("${app.data-cache:false}")
    private Boolean DATA_CACHE;

    @Override
    public List<StationWaterSupplyVO> getWaterSupplyInfo(String name, String type, String projectId, TenantId tenantId) {
        if (DATA_CACHE) {
            try {
                // 启用缓存
                BoundValueOperations<String, String> redis = stringRedisTemplate.boundValueOps("getWaterSupplyInfo_" + name + type + projectId + tenantId.getId().toString());
                String data = redis.get();
                if (StringUtils.isNotBlank(data)) {
                    return JSON.parseArray(data).toJavaList(StationWaterSupplyVO.class);
                }
            } catch (Exception e) {
                log.error("获取缓存数据失败!");
            }
        }

        List<StationWaterSupplyVO> resultList = new ArrayList<>();
        // 查询站点列表
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 9999, type, projectId);
        List<StationEntity> stationList = stationPageData.getData();
        if (stationList == null) {
            return new ArrayList<>();
        }
        // 筛选站点名称
        // if (StringUtils.isEmpty(name) || "overview".equals(name)) {
        //     name = "井";
        // }
        String finalName = name;
        stationList = stationList.stream().filter(station -> station.getName().contains(finalName)).collect(Collectors.toList());
        if (stationList.isEmpty()) {
            return new ArrayList<>();
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat tsDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 处理时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);

        // 月时间
        Date monthStart = instance.getTime();
        Date monthEnd = new Date();

        // 昨日时间
        Date yesterdayStart = new Date(monthEnd.getTime() - (24 * 60 * 60 * 1000));

        // 检查是否为月初的第一天, 若为第一天需要额外查询上一个月的最后一天的数据
        boolean isMonthStart = false;
        if (dateFormat.format(monthEnd).endsWith("-01")) {
            isMonthStart = true;
            yesterdayStart = new Date(monthStart.getTime() - (24 * 60 * 60 * 1000));
        }

        // 查询每个站点的数据
        for (StationEntity station : stationList) {
            StationWaterSupplyVO vo = new StationWaterSupplyVO();
            vo.setStationId(station.getId());
            vo.setName(station.getName());
            vo.setLocation(station.getLocation());
            vo.setImgs(station.getImgs());
            vo.setScadaUrl(station.getScadaUrl());

            resultList.add(vo);
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }
            long ts = -1;
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            List<String> attributes = new ArrayList<>();
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    attributes = Collections.singletonList(deviceId + "." + deviceFullData.getProperty());
                }
                if (deviceFullData.getCollectionTime() > ts) {
                    ts = deviceFullData.getCollectionTime();
                }
            }

            try {
                if (isMonthStart) {
                    totalFlowData = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), monthEnd.getTime(), DateUtils.DAY, null, tenantId);
                } else {
                    totalFlowData = obtainDataService.getDeviceData(attributes, monthStart.getTime(), monthEnd.getTime(), DateUtils.DAY, null, tenantId);
                }
            } catch (ThingsboardException e) {
                log.error("[查询站点数据异常] 查询站点类型: {}", type);
            }

            // 累加本月数据
            if (totalFlowData == null || totalFlowData.isEmpty()) {
                log.error("[查询站点数据异常] 查询站点类型: {}", type);
                continue;
            }
            BigDecimal monthTotalFlow = new BigDecimal("0");
            BigDecimal yesterdayTotalFlow = new BigDecimal("0");
            BigDecimal todayTotalFlow = new BigDecimal("0");
            if (isMonthStart) {// 月初第一天
                // 昨日数据
                LinkedHashMap<String, BigDecimal> yesterdayDataMap = totalFlowData.get(dateFormat.format(yesterdayStart.getTime()));
                for (Map.Entry<String, BigDecimal> dataEntry : yesterdayDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        yesterdayTotalFlow = yesterdayTotalFlow.add(flow);
                    }
                }
                // 今日、本月数据
                LinkedHashMap<String, BigDecimal> todayDataMap = totalFlowData.get(dateFormat.format(monthEnd.getTime()));
                for (Map.Entry<String, BigDecimal> dataEntry : todayDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        todayTotalFlow = todayTotalFlow.add(flow);
                        monthTotalFlow = monthTotalFlow.add(flow);
                    }
                }
            } else {// 非月初第一天
                // 本月数据
                for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : totalFlowData.entrySet()) {
                    LinkedHashMap<String, BigDecimal> value = entry.getValue();
                    for (Map.Entry<String, BigDecimal> dataEntry : value.entrySet()) {
                        BigDecimal flow = dataEntry.getValue();
                        if (flow != null) {
                            monthTotalFlow = monthTotalFlow.add(flow);
                        }
                    }
                }
                // 今日数据
                LinkedHashMap<String, BigDecimal> todayDataMap = totalFlowData.get(dateFormat.format(monthEnd.getTime()));
                for (Map.Entry<String, BigDecimal> dataEntry : todayDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        todayTotalFlow = todayTotalFlow.add(flow);
                    }
                }
                // 昨日数据
                LinkedHashMap<String, BigDecimal> yesterdayDataMap = totalFlowData.get(dateFormat.format(yesterdayStart.getTime()));
                for (Map.Entry<String, BigDecimal> dataEntry : yesterdayDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        yesterdayTotalFlow = yesterdayTotalFlow.add(flow);
                    }
                }
            }

            vo.setMonthWaterSupply(monthTotalFlow);
            vo.setTodayWaterSupply(todayTotalFlow);
            vo.setYesterdayWaterSupply(yesterdayTotalFlow);
            vo.setLastTime(tsDateFormat.format(new Date(ts)));

        }

        if (DATA_CACHE) {
            try {
                // 启用缓存
                Random random = new Random();
                int nextInt = random.nextInt(60);
                BoundValueOperations<String, String> redis = stringRedisTemplate.boundValueOps("getWaterSupplyInfo_" + name + type + projectId + tenantId.getId().toString());
                redis.set(JSON.toJSONString(resultList), 240 + nextInt, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("保存缓存数据失败!");
            }
        }

        return resultList;
    }

    @Override
    public JSONObject getWaterSupplyDetail(String stationId, TenantId tenantId) throws ThingsboardException {
        if (DATA_CACHE) {
            try {
                // 启用缓存
                BoundValueOperations<String, String> redis = stringRedisTemplate.boundValueOps("getWaterSupplyInfo_" + stationId + tenantId.getId().toString());
                String data = redis.get();
                if (StringUtils.isNotBlank(data)) {
                    return JSON.parseObject(data);
                }
            } catch (Exception e) {
                log.error("获取缓存数据失败!");
            }
        }
        // 查询站点列表
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 处理时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);

        // 月时间
        Date monthStart = instance.getTime();
        Date monthEnd = new Date();

        // 今日时间
        instance.setTime(new Date());
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        // 昨日时间
        Date yesterdayStart = new Date(todayStart.getTime() - (24 * 60 * 60 * 1000));
        Date yesterdayEnd = new Date(todayStart.getTime() - 1000);

        // 查询本月供水量曲线
        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);
        List<String> attributes = new ArrayList<>();
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    attributes.add(deviceId + "." + deviceFullData.getProperty());
                    break;
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", stationId);
                }
            }
        }

        // 月数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> monthTotalFlowData = obtainDataService.getDeviceData(attributes, monthStart.getTime(), monthEnd.getTime(), DateUtils.DAY, null, tenantId);

        // 本日数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> todayTotalFlowData = obtainDataService.getDeviceData(attributes, todayStart.getTime(), monthEnd.getTime(), DateUtils.HOUR, null, tenantId);

        // 昨日数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> yesterdayTotalFlowData = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), yesterdayEnd.getTime(), DateUtils.HOUR, null, tenantId);

        // 将MAP数据处理成数组
        List<JSONObject> monthTotalFlowDataList = StationDataUtil.dataMapToList(monthTotalFlowData);
        List<JSONObject> todayTotalFlowDataList = StationDataUtil.dataMapToList(todayTotalFlowData);
        List<JSONObject> yesterdayTotalFlowDataList = StationDataUtil.dataMapToList(yesterdayTotalFlowData);

        // 返回数据
        JSONObject result = new JSONObject();
        result.put("monthTotalFlowDataList", monthTotalFlowDataList);
        result.put("todayTotalFlowDataList", todayTotalFlowDataList);
        result.put("yesterdayTotalFlowDataList", yesterdayTotalFlowDataList);

        if (DATA_CACHE) {
            try {
                // 启用缓存
                Random random = new Random();
                int nextInt = random.nextInt(60);
                BoundValueOperations<String, String> redis = stringRedisTemplate.boundValueOps("getWaterSupplyInfo_" + stationId + tenantId.getId().toString());
                redis.set(JSON.toJSONString(result), 240 + nextInt, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("保存缓存数据失败!");
            }
        }

        return result;
    }

    @Override
    public Map<String, List<JSONObject>> getStationData(String stationId, String groupType, List<String> attrList, String queryType, Date start, Date end, TenantId tenantId) throws ThingsboardException {
        Map<String, List<JSONObject>> resultMap = new LinkedHashMap<>();
        // 查询站点
        StationEntity station = stationFeignClient.get(stationId);

        // 查询数据
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        List<DeviceFullData> stationDataDetail = new ArrayList<>();
        if (StringUtils.isNotBlank(groupType)) {
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(groupType)) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                throw new ThingsboardException("水厂未设置" + groupType + "相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }

            stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);
        } else {
            stationDataDetail = stationDataService.getStationDataDetail(station.getId(), "", true, tenantId);
        }

        List<String> attributes = new ArrayList<>();
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if (attrList.contains(deviceFullData.getProperty())) {
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    attributes.add(deviceId + "." + deviceFullData.getProperty());

                    resultMap.put(deviceFullData.getProperty(), new ArrayList<>());
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", stationId);
                }
            }
        }

        // 查询数据
        if (attributes.size() == 0) {
            log.error("[该站点没有当前查询属性] 查询站点ID: {}", stationId);
            return resultMap;
        }
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> queryDataResult = obtainDataService.getDeviceData(attributes, start.getTime(), end.getTime(), queryType, null, tenantId);

        // 数据筛选
        String suffix = "";
        if ("hour".equals(queryType)) {
            suffix = "时";
        }
        if ("day".equals(queryType)) {
            suffix = "日";
        }
        if ("month".equals(queryType)) {
            suffix = "月";
        }
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : queryDataResult.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                String dataKey = dataEntry.getKey();
                String[] keyArray = dataKey.split("\\.");
                if (resultMap.containsKey(keyArray[1])) {
                    List<JSONObject> list = resultMap.get(keyArray[1]);
                    JSONObject data = new JSONObject();
                    data.put("ts", StationDataUtil.shortenTimeKey(key, queryType) + suffix);
                    data.put("value", dataEntry.getValue());

                    list.add(data);
                    resultMap.put(keyArray[1], list);
                }
            }

        }

        return resultMap;
    }

    @Override
    public StationWaterSupplyViewVO getWaterSupplyInfoView(String type, String projectId, TenantId tenantId) {
        // 返回结果
        StationWaterSupplyViewVO viewResult = new StationWaterSupplyViewVO();
        // 查询站点列表
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 9999, type, projectId);
        List<StationEntity> stationList = stationPageData.getData();
        if (stationList == null) {
            return viewResult;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 处理时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);

        // 本月时间
        Date monthStart = instance.getTime();
        Date monthEnd = new Date();

        // 上月时间
        instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
        Date lastMonthStart = instance.getTime();
        Date lastMonthEnd = new Date(monthStart.getTime() - 1000);

        // 昨日时间
        Date yesterdayStart = new Date(monthEnd.getTime() - (24 * 60 * 60 * 1000));

        // 检查是否为月初的第一天, 若为第一天需要额外查询上一个月的最后一天的数据
        boolean isMonthStart = false;
        if (dateFormat.format(monthEnd).endsWith("-01")) {
            isMonthStart = true;
            yesterdayStart = new Date(monthStart.getTime() - (24 * 60 * 60 * 1000));
        }

        // 要查询的属性列表
        List<String> attributes = new ArrayList<>();
        BigDecimal totalFlow = new BigDecimal("0");
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        attributes.add(deviceId + "." + deviceFullData.getProperty());
                        if (StringUtils.isNotBlank(deviceFullData.getValue())) {
                            totalFlow = totalFlow.add(new BigDecimal(deviceFullData.getValue()));
                        }
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
        }

        // 本月
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> monthDataMap = null;
        // 上月
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> lastMonthDataMap = null;

        // 查询本月数据
        try {
            monthDataMap = obtainDataService.getDeviceData(attributes, monthStart.getTime(), monthEnd.getTime(), DateUtils.DAY, null, tenantId);
        } catch (ThingsboardException e) {
            log.error("[查询供水量总览]查询本月数据异常");
        }

        // 查询上月数据
        // 查询本月数据
        try {
            lastMonthDataMap = obtainDataService.getDeviceData(attributes, lastMonthStart.getTime(), lastMonthEnd.getTime(), DateUtils.DAY, null, tenantId);
        } catch (ThingsboardException e) {
            log.error("[查询供水量总览]查询本月数据异常");
        }

        // 筛选今日数据
        BigDecimal todayTotalFlow = new BigDecimal("0");
        if (monthDataMap != null && monthDataMap.size() > 0) {
            LinkedHashMap<String, BigDecimal> todayDataMap = monthDataMap.get(dateFormat.format(monthEnd));
            for (Map.Entry<String, BigDecimal> dataEntry : todayDataMap.entrySet()) {
                BigDecimal flow = dataEntry.getValue();
                if (flow != null) {
                    todayTotalFlow = todayTotalFlow.add(flow);
                }
            }
        }

        // 筛选昨日数据
        BigDecimal yesterdayTotalFlow = new BigDecimal("0");
        if (lastMonthDataMap != null && lastMonthDataMap.size() > 0) {
            LinkedHashMap<String, BigDecimal> yesterdayDataMap;
            if (isMonthStart) {
                yesterdayDataMap = lastMonthDataMap.get(dateFormat.format(yesterdayStart));
            } else {
                if (monthDataMap == null) {
                    yesterdayDataMap = new LinkedHashMap<>();
                } else {
                    yesterdayDataMap = monthDataMap.get(dateFormat.format(yesterdayStart));
                }
            }
            for (Map.Entry<String, BigDecimal> dataEntry : yesterdayDataMap.entrySet()) {
                BigDecimal flow = dataEntry.getValue();
                if (flow != null) {
                    yesterdayTotalFlow = yesterdayTotalFlow.add(flow);
                }
            }
        }

        // 统计本月数据
        BigDecimal monthTotalFlow = new BigDecimal("0");
        if (monthDataMap != null && monthDataMap.size() > 0) {
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : monthDataMap.entrySet()) {
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                if (dataMap != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        BigDecimal flow = dataEntry.getValue();
                        if (flow != null) {
                            monthTotalFlow = monthTotalFlow.add(flow);
                        }
                    }
                }
            }
        }

        // 统计上月数据
        BigDecimal lastMonthTotalFlow = new BigDecimal("0");
        if (lastMonthDataMap != null && lastMonthDataMap.size() > 0) {
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : lastMonthDataMap.entrySet()) {
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                if (dataMap != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        BigDecimal flow = dataEntry.getValue();
                        if (flow != null) {
                            lastMonthTotalFlow = lastMonthTotalFlow.add(flow);
                        }
                    }
                }
            }
        }

        viewResult.setMonthWaterSupply(monthTotalFlow);
        viewResult.setLastMonthWaterSupply(lastMonthTotalFlow);
        viewResult.setYesterdayWaterSupply(yesterdayTotalFlow);
        viewResult.setTodayWaterSupply(todayTotalFlow);
        viewResult.setTotalWaterSupply(totalFlow);

        //TODO 2025/4/9
        // 报警数
//        long num = alarmCountRepository.getAlarmCountByType(type, UUIDConverter.fromTimeUUID(tenantId.getId()));
//
//        viewResult.setAlarmNum(num);

        return viewResult;
    }

    @Override
    public DynamicTableVO getWaterSupplyReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 报表类型
        String prefix = "";
        switch (queryType) {
            case "day":
                queryType = "1h";
                prefix = "时";
                break;
            case "month":
                queryType = "day";
                prefix = "日";
                break;
            case "year":
                queryType = "month";
                prefix = "月";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

        List<String> attributes = new ArrayList<>();
        String unit = "";
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    attributes.add(deviceId + "." + deviceFullData.getProperty());
                    unit = deviceFullData.getUnit();
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }

        // 查询出水累计流量
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attributes, start, end, queryType, null, tenantId);

        // 按报表类型筛选数据
        List<String> allTimeKeyList = new ArrayList<>(stationDataMap.keySet());
        Map<String, List<String>> timeKeyMap = new LinkedHashMap<>();

        // 将数据时间分组
        for (String timeKey : allTimeKeyList) {
            String timeSubstring = "";
            if (queryType.equals("month")) {// 年报表
                timeSubstring = timeKey.substring(0, 4);// 取年份
            } else if (queryType.equals("day")) {// 月报表
                timeSubstring = timeKey.substring(0, 7);// 取月份
            } else {// 日报表
                timeSubstring = timeKey.substring(0, 10);// 取日期
            }
            List<String> timeKeyList = new ArrayList<>();
            if (timeKeyMap.containsKey(timeSubstring)) {
                timeKeyList = timeKeyMap.get(timeSubstring);
            }

            timeKeyList.add(timeKey);
            timeKeyMap.put(timeSubstring, timeKeyList);
        }

        // 数据分组
        Map<String, JSONObject> dataMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeKey = entry.getValue();
            for (String time : timeKey) {
                JSONObject data = new JSONObject();
                data.put("ts", StationDataUtil.shortenTimeKey(time, queryType) + prefix);
                dataMap.put(StationDataUtil.shortenTimeKey(time, queryType), data);
            }
        }

        // 设置数据
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeList = entry.getValue();

            for (String time : timeList) {
                JSONObject data = dataMap.get(StationDataUtil.shortenTimeKey(time, queryType));
                BigDecimal flow = null;
                LinkedHashMap<String, BigDecimal> dataValueMap = stationDataMap.get(time);
                if (dataValueMap != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataValueMap.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (flow == null) {
                                flow = new BigDecimal("0");
                            }
                            flow = flow.add(dataEntry.getValue());
                        }
                    }
                }
                data.put(key, flow);

                BigDecimal max = flow;
                BigDecimal min = flow;
                BigDecimal total = new BigDecimal("0");
                if (data.getBigDecimal("max") == null || (max != null && data.getBigDecimal("max").doubleValue() < max.doubleValue())) {
                    data.put("max", max);
                }
                if (data.getBigDecimal("min") == null || (min != null && data.getBigDecimal("min").doubleValue() > min.doubleValue())) {
                    data.put("min", min);
                }
                if (data.getBigDecimal("total") != null) {
                    total = data.getBigDecimal("total");
                }
                if (flow != null) {
                    data.put("total", total.add(flow));
                }
            }

        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (String key : timeKeyMap.keySet()) {
            DeviceDataTableInfoVO dataTableInfoVO = new DeviceDataTableInfoVO();
            dataTableInfoVO.setColumnName(key);
            dataTableInfoVO.setColumnValue(key);
            dataTableInfoVO.setUnit(unit);
            deviceDataTableInfoList.add(dataTableInfoVO);
        }
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("min:最小值:" + unit, "max:最大值:" + unit, "total:总计:" + unit));

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);

        // 数据补全
        Map<String, JSONObject> newDataMap = new LinkedHashMap<>();
        switch (queryType) {
            case "1h":
                newDataMap = dataMap;
                break;
            case "day":
                newDataMap = dataMap;
                break;
            case "month":
                for (int i = 1; i <= 12; i++) {
                    JSONObject data = dataMap.get(String.format("%02d", i));
                    if (data == null) {
                        data = new JSONObject();
                        data.put("ts", i + prefix);

                    }
                    newDataMap.put(i + "", data);
                }
                break;
        }
        // 补全统计数据
        JSONObject maxObj = new JSONObject();
        maxObj.put("ts", "最大值");
        JSONObject minObj = new JSONObject();
        minObj.put("ts", "最小值");
        JSONObject avgObj = new JSONObject();
        avgObj.put("ts", "平均值");
        JSONObject totalObj = new JSONObject();
        totalObj.put("ts", "合计");

        newDataMap.put("min", minObj);
        newDataMap.put("max", maxObj);
        newDataMap.put("avg", avgObj);
        newDataMap.put("total", totalObj);

        // 统计最大值、最小值、平均值、合计
        for (String timeKey : timeKeyMap.keySet()) {
            BigDecimal max = null;
            BigDecimal min = null;
            BigDecimal avg = null;
            BigDecimal total = null;
            int i = 0;
            for (Map.Entry<String, JSONObject> entry : newDataMap.entrySet()) {
                JSONObject object = entry.getValue();

                BigDecimal data = object.getBigDecimal(timeKey);
                if (data == null) {
                    continue;
                }
                if (total == null) {
                    total = new BigDecimal("0");
                }
                total = total.add(data);
                if (max == null || data.doubleValue() > max.doubleValue()) {
                    max = data;
                }
                if (min == null || data.doubleValue() < min.doubleValue()) {
                    min = data;
                }
                i++;
            }

            if (total != null && i != 0) {
                // 计算平均值
                avg = total.divide(BigDecimal.valueOf(i), 2, BigDecimal.ROUND_DOWN);
            }

            // 添加统计数据到列表
            JSONObject maxData = newDataMap.get("max");
            maxData.put(timeKey, max);

            JSONObject minData = newDataMap.get("min");
            minData.put(timeKey, min);

            JSONObject avgData = newDataMap.get("avg");
            avgData.put(timeKey, avg);

            JSONObject totalData = newDataMap.get("total");
            totalData.put(timeKey, total);
        }

        JSONObject maxData = newDataMap.get("max");
        countData(timeKeyMap, maxData);
        JSONObject minData = newDataMap.get("min");
        countData(timeKeyMap, minData);
        JSONObject avgData = newDataMap.get("avg");
        countData(timeKeyMap, avgData);
        JSONObject totalData = newDataMap.get("total");
        countData(timeKeyMap, totalData);

        List<JSONObject> dataList = new ArrayList<>(newDataMap.values());
        result.setTableDataList(dataList);

        return result;
    }

    /**
     * 按时间map统计数据的最小值最大值合计值
     *
     * @param timeKeyMap 时间map
     * @param dataObj    数据
     */
    private void countData(Map<String, List<String>> timeKeyMap, JSONObject dataObj) {
        BigDecimal maxDataMaxValue = null;
        BigDecimal minDataMaxValue = null;
        BigDecimal totalDataMaxValue = null;
        for (String timeKey : timeKeyMap.keySet()) {
            BigDecimal data = dataObj.getBigDecimal(timeKey);
            if (maxDataMaxValue == null || (data != null && data.doubleValue() > maxDataMaxValue.doubleValue())) {
                maxDataMaxValue = data;
            }
            if (minDataMaxValue == null || (data != null && data.doubleValue() < minDataMaxValue.doubleValue())) {
                minDataMaxValue = data;
            }
            if (data != null) {
                if (totalDataMaxValue == null) {
                    totalDataMaxValue = new BigDecimal("0");
                }
                totalDataMaxValue = totalDataMaxValue.add(data);
            }
        }
        dataObj.put("max", maxDataMaxValue);
        dataObj.put("min", minDataMaxValue);
        dataObj.put("total", totalDataMaxValue);
    }

    @Override
    public DynamicTableVO getEnergyMethodReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 报表类型
        String suffix = "";
        switch (queryType) {
            case "day":
                queryType = "1h";
                suffix = "时";
                break;
            case "month":
                queryType = "day";
                suffix = "日";
                break;
            case "year":
                queryType = "month";
                suffix = "月";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

        List<String> attributes = new ArrayList<>();
        String unit = "";
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if (deviceFullData.getProperty().contains(DataConstants.DeviceAttrType.ENERGY_IN.getValue())) {// 累计用能
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    attributes.add(deviceId + "." + deviceFullData.getProperty());
                    unit = deviceFullData.getUnit();
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }

        // 查询累计用能
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attributes, start, end, queryType, null, tenantId);

        // 按报表类型筛选数据
        List<String> allTimeKeyList = new ArrayList<>(stationDataMap.keySet());
        Map<String, List<String>> timeKeyMap = new LinkedHashMap<>();

        // 将数据时间分组
        for (String timeKey : allTimeKeyList) {
            String timeSubstring = "";
            if (queryType.equals("month")) {// 年报表
                timeSubstring = timeKey.substring(0, 4);// 取年份
            } else if (queryType.equals("day")) {// 月报表
                timeSubstring = timeKey.substring(0, 7);// 取月份
            } else {// 日报表
                timeSubstring = timeKey.substring(0, 10);// 取日期
            }
            List<String> timeKeyList = new ArrayList<>();
            if (timeKeyMap.containsKey(timeSubstring)) {
                timeKeyList = timeKeyMap.get(timeSubstring);
            }

            timeKeyList.add(timeKey);
            timeKeyMap.put(timeSubstring, timeKeyList);
        }

        // 数据分组
        Map<String, JSONObject> dataMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeKey = entry.getValue();
            for (String time : timeKey) {
                JSONObject data = new JSONObject();
                data.put("ts", StationDataUtil.shortenTimeKey(time, queryType) + suffix);
                dataMap.put(StationDataUtil.shortenTimeKey(time, queryType), data);
            }
        }

        // 设置数据
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeList = entry.getValue();

            for (String time : timeList) {
                JSONObject data = dataMap.get(StationDataUtil.shortenTimeKey(time, queryType));
                BigDecimal flow = null;
                LinkedHashMap<String, BigDecimal> dataValueMap = stationDataMap.get(time);
                if (dataValueMap != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataValueMap.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (flow == null) {
                                flow = new BigDecimal("0");
                            }
                            flow = flow.add(dataEntry.getValue());
                        }
                    }
                }
                data.put(key, flow);

                BigDecimal max = flow;
                BigDecimal min = flow;
                BigDecimal total = new BigDecimal("0");
                if (data.getBigDecimal("max") == null || (max != null && data.getBigDecimal("max").doubleValue() < max.doubleValue())) {
                    data.put("max", max);
                }
                if (data.getBigDecimal("min") == null || (min != null && data.getBigDecimal("min").doubleValue() > min.doubleValue())) {
                    data.put("min", min);
                }
                if (data.getBigDecimal("total") != null) {
                    total = data.getBigDecimal("total");
                }
                if (flow != null) {
                    data.put("total", total.add(flow));
                }
            }

        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (String key : timeKeyMap.keySet()) {
            DeviceDataTableInfoVO dataTableInfoVO = new DeviceDataTableInfoVO();
            dataTableInfoVO.setColumnName(key);
            dataTableInfoVO.setColumnValue(key);
            dataTableInfoVO.setUnit(unit);
            deviceDataTableInfoList.add(dataTableInfoVO);
        }
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("min:最小值:" + unit, "max:最大值:" + unit, "total:总计:" + unit));

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);

        // 数据补全
        Map<String, JSONObject> newDataMap = new LinkedHashMap<>();
        switch (queryType) {
            case "1h":
                newDataMap = dataMap;
                break;
            case "day":
                newDataMap = dataMap;
                break;
            case "month":
                for (int i = 1; i <= 12; i++) {
                    JSONObject data = dataMap.get(String.format("%02d", i));
                    if (data == null) {
                        data = new JSONObject();
                        data.put("ts", i + suffix);

                    }
                    newDataMap.put(i + "", data);
                }
                break;
        }
        // 补全统计数据
        JSONObject maxObj = new JSONObject();
        maxObj.put("ts", "最大值");
        JSONObject minObj = new JSONObject();
        minObj.put("ts", "最小值");
        JSONObject avgObj = new JSONObject();
        avgObj.put("ts", "平均值");
        JSONObject totalObj = new JSONObject();
        totalObj.put("ts", "合计");

        newDataMap.put("min", minObj);
        newDataMap.put("max", maxObj);
        newDataMap.put("avg", avgObj);
        newDataMap.put("total", totalObj);

        // 统计最大值、最小值、平均值、合计
        for (String timeKey : timeKeyMap.keySet()) {
            BigDecimal max = null;
            BigDecimal min = null;
            BigDecimal avg = null;
            BigDecimal total = null;
            int i = 0;
            for (Map.Entry<String, JSONObject> entry : newDataMap.entrySet()) {
                JSONObject object = entry.getValue();

                BigDecimal data = object.getBigDecimal(timeKey);
                if (data == null) {
                    continue;
                }
                if (total == null) {
                    total = new BigDecimal("0");
                }
                total = total.add(data);
                if (max == null || data.doubleValue() > max.doubleValue()) {
                    max = data;
                }
                if (min == null || data.doubleValue() < min.doubleValue()) {
                    min = data;
                }
                i++;
            }

            if (total != null && i != 0) {
                // 计算平均值
                avg = total.divide(BigDecimal.valueOf(i), 2, BigDecimal.ROUND_DOWN);
            }

            // 添加统计数据到列表
            JSONObject maxData = newDataMap.get("max");
            maxData.put(timeKey, max);

            JSONObject minData = newDataMap.get("min");
            minData.put(timeKey, min);

            JSONObject avgData = newDataMap.get("avg");
            avgData.put(timeKey, avg);

            JSONObject totalData = newDataMap.get("total");
            totalData.put(timeKey, total);
        }

        JSONObject maxData = newDataMap.get("max");
        countData(timeKeyMap, maxData);
        JSONObject minData = newDataMap.get("min");
        countData(timeKeyMap, minData);
        JSONObject avgData = newDataMap.get("avg");
        countData(timeKeyMap, avgData);
        JSONObject totalData = newDataMap.get("total");
        countData(timeKeyMap, totalData);

        List<JSONObject> dataList = new ArrayList<>(newDataMap.values());
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public DynamicTableVO getWaterSupplyConsumptionReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 报表类型
        String prefix = "";
        switch (queryType) {
            case "day":
                queryType = "1h";
                prefix = "时";
                break;
            case "month":
                queryType = "day";
                prefix = "日";
                break;
            case "year":
                queryType = "month";
                prefix = "月";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

        List<String> flowAttributes = new ArrayList<>();
        String flowUnit = "";
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    flowAttributes.add(deviceId + "." + deviceFullData.getProperty());
                    flowUnit = deviceFullData.getUnit();
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }

        // 查询出水累计流量
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationFlowDataMap = new LinkedHashMap<>();
        try {
            stationFlowDataMap = obtainDataService.getDeviceData(flowAttributes, start, end, queryType, null, tenantId);
        } catch (ThingsboardException e) {
            log.error("查询数据失败, 原因: {}", e.getMessage());
        }

        List<String> energyAttributes = new ArrayList<>();
        String energyUnit = "";
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(deviceFullData.getProperty())) {// 有功电能
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    energyAttributes.add(deviceId + "." + deviceFullData.getProperty());
                    energyUnit = deviceFullData.getUnit();
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }

        // 查询电耗
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationEnergyDataMap = new LinkedHashMap<>();
        try {
            stationEnergyDataMap = obtainDataService.getDeviceData(energyAttributes, start, end, queryType, null, tenantId);
        } catch (ThingsboardException e) {
            log.error("查询数据失败, 原因: {}", e.getMessage());
        }

        // 计算单耗
        List<String> allTimeKeyList = new ArrayList<>(stationFlowDataMap.keySet());
        // 单耗数据MAP
        LinkedHashMap<String, BigDecimal> waterSupplyConsumptionDataMap = new LinkedHashMap<>();
        for (String timeKey : allTimeKeyList) {
            LinkedHashMap<String, BigDecimal> flowDataMap = stationFlowDataMap.get(timeKey);
            LinkedHashMap<String, BigDecimal> energyDataMap = stationEnergyDataMap.get(timeKey);

            BigDecimal flowTotal = new BigDecimal("0");
            BigDecimal energyTotal = new BigDecimal("0");
            if (flowDataMap != null && !flowDataMap.isEmpty()) {
                for (Map.Entry<String, BigDecimal> dataEntry : flowDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        flowTotal = flowTotal.add(flow);
                    }
                }
            }
            if (energyDataMap != null && !energyDataMap.isEmpty()) {
                for (Map.Entry<String, BigDecimal> dataEntry : energyDataMap.entrySet()) {
                    BigDecimal energy = dataEntry.getValue();
                    if (energy != null) {
                        energyTotal = energyTotal.add(energy);
                    }
                }
            }

            // 单耗
            BigDecimal waterSupplyConsumption = null;
            if (flowTotal.doubleValue() != 0 && energyTotal.doubleValue() != 0) {
                waterSupplyConsumption = energyTotal.divide(flowTotal, 2, BigDecimal.ROUND_DOWN);
            }

            waterSupplyConsumptionDataMap.put(timeKey, waterSupplyConsumption);
        }

        // 筛选数据, 按报表分组
        Map<String, List<String>> timeKeyMap = new LinkedHashMap<>();

        // 将数据时间分组
        for (String timeKey : allTimeKeyList) {
            String timeSubstring = "";
            if (queryType.equals("month")) {// 年报表
                timeSubstring = timeKey.substring(0, 4);// 取年份
            } else if (queryType.equals("day")) {// 月报表
                timeSubstring = timeKey.substring(0, 7);// 取月份
            } else {// 日报表
                timeSubstring = timeKey.substring(0, 10);// 取日期
            }
            List<String> timeKeyList = new ArrayList<>();
            if (timeKeyMap.containsKey(timeSubstring)) {
                timeKeyList = timeKeyMap.get(timeSubstring);
            }

            timeKeyList.add(timeKey);
            timeKeyMap.put(timeSubstring, timeKeyList);
        }

        // 数据分组
        Map<String, JSONObject> dataMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            List<String> timeKey = entry.getValue();
            for (String time : timeKey) {
                JSONObject data = new JSONObject();
                data.put("ts", StationDataUtil.shortenTimeKey(time, queryType) + prefix);
                dataMap.put(StationDataUtil.shortenTimeKey(time, queryType), data);
            }
        }

        // 设置数据
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeList = entry.getValue();

            for (String time : timeList) {
                JSONObject data = dataMap.get(StationDataUtil.shortenTimeKey(time, queryType));
                BigDecimal flow = waterSupplyConsumptionDataMap.get(time);
                data.put(key, flow);

                if (flow == null) {
                    continue;
                }
                BigDecimal total = new BigDecimal("0");
                if (data.getBigDecimal("max") == null || (data.getBigDecimal("max") != null && data.getBigDecimal("max").doubleValue() < flow.doubleValue())) {
                    data.put("max", flow);
                }
                if (data.getBigDecimal("min") == null || (data.getBigDecimal("min") != null && data.getBigDecimal("min").doubleValue() > flow.doubleValue())) {
                    data.put("min", flow);
                }
                if (data.getBigDecimal("total") != null) {
                    total = data.getBigDecimal("total");
                }
                data.put("total", total.add(flow));
            }

        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (String key : timeKeyMap.keySet()) {
            DeviceDataTableInfoVO dataTableInfoVO = new DeviceDataTableInfoVO();
            dataTableInfoVO.setColumnName(key);
            dataTableInfoVO.setColumnValue(key);
            dataTableInfoVO.setUnit("KWh/10³m³");
            deviceDataTableInfoList.add(dataTableInfoVO);
        }
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("min:最小值:KWh/10³m³", "max:最大值:KWh/10³m³", "avg:平均值:KWh/10³m³"));

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);

        // 数据补全
        Map<String, JSONObject> newDataMap = new LinkedHashMap<>();
        switch (queryType) {
            case "1h":
                newDataMap = dataMap;
                break;
            case "day":
                newDataMap = dataMap;
                break;
            case "month":
                for (int i = 1; i <= 12; i++) {
                    JSONObject data = dataMap.get(i + "");
                    if (data == null) {
                        data = new JSONObject();
                        data.put("ts", i + prefix);

                    }
                    newDataMap.put(i + "", data);
                }
                break;
        }
        // 补全统计数据
        JSONObject maxObj = new JSONObject();
        maxObj.put("ts", "最大值");
        JSONObject minObj = new JSONObject();
        minObj.put("ts", "最小值");
        JSONObject avgObj = new JSONObject();
        avgObj.put("ts", "平均值");
        JSONObject totalObj = new JSONObject();
        totalObj.put("ts", "合计");

        newDataMap.put("min", minObj);
        newDataMap.put("max", maxObj);
        newDataMap.put("avg", avgObj);
        newDataMap.put("total", totalObj);

        // 统计最大值、最小值、平均值、合计
        for (String timeKey : timeKeyMap.keySet()) {
            BigDecimal max = null;
            BigDecimal min = null;
            BigDecimal avg = null;
            BigDecimal total = null;
            int i = 0;
            for (Map.Entry<String, JSONObject> entry : newDataMap.entrySet()) {
                JSONObject object = entry.getValue();

                BigDecimal data = object.getBigDecimal(timeKey);
                if (data == null) {
                    continue;
                }
                if (total == null) {
                    total = new BigDecimal("0");
                }
                total = total.add(data);
                if (max == null || data.doubleValue() > max.doubleValue()) {
                    max = data;
                }
                if (min == null || data.doubleValue() < min.doubleValue()) {
                    min = data;
                }
                i++;
            }

            if (total != null && i != 0) {
                // 计算平均值
                avg = total.divide(BigDecimal.valueOf(i), 2, BigDecimal.ROUND_DOWN);
            }

            // 添加统计数据到列表
            JSONObject maxData = newDataMap.get("max");
            maxData.put(timeKey, max);

            JSONObject minData = newDataMap.get("min");
            minData.put(timeKey, min);

            JSONObject avgData = newDataMap.get("avg");
            avgData.put(timeKey, avg);

            JSONObject totalData = newDataMap.get("total");
            totalData.put(timeKey, total);
        }

        JSONObject maxData = newDataMap.get("max");
        countData(timeKeyMap, maxData);
        JSONObject minData = newDataMap.get("min");
        countData(timeKeyMap, minData);
        JSONObject avgData = newDataMap.get("avg");
        countData(timeKeyMap, avgData);
        JSONObject totalData = newDataMap.get("total");
        countData(timeKeyMap, totalData);

        // 计算平均值
        for (Map.Entry<String, JSONObject> entry : dataMap.entrySet()) {
            JSONObject data = entry.getValue();
            if (data.getBigDecimal("total") != null && timeKeyMap.size() > 0) {
                data.put("avg", data.getBigDecimal("total").divide(BigDecimal.valueOf(timeKeyMap.size()), 2, BigDecimal.ROUND_DOWN));
            }
        }

        List<JSONObject> dataList = new ArrayList<>(newDataMap.values());
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public DynamicTableVO getWaterSupplyDetailReport(String stationType, List<String> stationIdList,
                                                     Long start, Long end, String queryType, TenantId tenantId, boolean count) throws ThingsboardException {
        // 返回的列表数据预设
        Map<Integer, JSONObject> resultDataMap = new LinkedHashMap<>();
        // 报表类型
        String suffix = "";
        switch (queryType) {
            case "day":
                suffix = "时";
                queryType = "1h";
                for (int i = 0; i < 24; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            case "month":
                suffix = "日";
                queryType = "day";
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date(start));
                int days = instance.getActualMaximum(Calendar.DAY_OF_MONTH);
                for (int i = 1; i <= days; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            case "year":
                suffix = "月";
                queryType = "month";
                for (int i = 1; i <= 12; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);

        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 查询站点供水量数据
        Map<String, List<String>> attributesMap = new LinkedHashMap<>();
        String unit = "";
        for (StationEntity station : stationList) {
            List<String> attributes = new ArrayList<>();
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);


            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        attributes.add(deviceId + "." + deviceFullData.getProperty());
                        unit = deviceFullData.getUnit();
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
            attributesMap.put(station.getId(), attributes);
        }


        // 固定行
        JSONObject maxObj = new JSONObject();
        maxObj.put("ts", "最大值");
        JSONObject maxTimeObj = new JSONObject();
        maxTimeObj.put("ts", "最大值时间");
        JSONObject minObj = new JSONObject();
        minObj.put("ts", "最小值");
        JSONObject minTimeObj = new JSONObject();
        minTimeObj.put("ts", "最小值时间");
        JSONObject avgObj = new JSONObject();
        avgObj.put("ts", "平均值");
        JSONObject totalObj = new JSONObject();
        totalObj.put("ts", "合计");

        // 分组查询数据
        for (Map.Entry<String, List<String>> attrEntry : attributesMap.entrySet()) {
            String key = attrEntry.getKey();
            List<String> attrList = attrEntry.getValue();
            if (attrList == null || attrList.isEmpty()) {
                continue;
            }

            // 查询数据
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attrList, start, end, queryType, null, tenantId);

            BigDecimal total = new BigDecimal("0");
            BigDecimal max = null;
            BigDecimal min = null;
            String maxFlag = "-";// 记录最大值的时间
            String minFlag = "-";// 记录最小值的时间
            int num = 0; // 次数

            // 合计Map
            Map<Integer, BigDecimal> totalMap = new LinkedHashMap<>();

            // 设置站点供水量数据
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String timeKey = entry.getKey();
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                BigDecimal flow = new BigDecimal("0");
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    if (dataEntry.getValue() != null) {
                        flow = flow.add(dataEntry.getValue());
                    }
                }

                total = total.add(flow);
                num++;
                if (max == null || max.doubleValue() < flow.doubleValue()) {
                    max = flow;
                    maxFlag = StationDataUtil.shortenTimeKey(timeKey, queryType);
                }
                if (min == null || min.doubleValue() > flow.doubleValue()) {
                    min = flow;
                    minFlag = StationDataUtil.shortenTimeKey(timeKey, queryType);
                }

                int resultMapKey = Integer.parseInt(StationDataUtil.shortenTimeKey(timeKey, queryType));
                JSONObject data = resultDataMap.get(resultMapKey);
                data.put(key, flow);

                resultDataMap.put(resultMapKey, data);

                // 设置合计map
                BigDecimal nowTotal = new BigDecimal("0");
                if (totalMap.containsKey(resultMapKey)) {
                    nowTotal = totalMap.get(resultMapKey);
                }
                nowTotal = nowTotal.add(flow);
                totalMap.put(resultMapKey, nowTotal);
            }

            // 设置最大最小值平均值等数据
            if (max != null) {
                maxObj.put(key, max);
                maxTimeObj.put(key, Integer.parseInt(maxFlag) + suffix);
            }
            if (min != null) {
                minObj.put(key, min);
                minTimeObj.put(key, minFlag + suffix);
            }
            // 平均值
            if (num != 0) {
                avgObj.put(key, total.divide(BigDecimal.valueOf(num), 2, BigDecimal.ROUND_DOWN));
            }
            // 合计
            totalObj.put(key, total);
        }

        // 设置固定行数据
        List<JSONObject> dataList = new ArrayList<>(resultDataMap.values());
        if (count) {
            dataList.add(maxObj);
            dataList.add(maxTimeObj);
            dataList.add(minObj);
            dataList.add(minTimeObj);
            dataList.add(avgObj);
            dataList.add(totalObj);
        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间", "total:合计:" + unit));

        // 动态结构
        for (StationEntity station : stationList) {
            DeviceDataTableInfoVO tableInfoVO = new DeviceDataTableInfoVO();
            tableInfoVO.setUnit(unit);
            tableInfoVO.setColumnName(station.getName());
            tableInfoVO.setColumnValue(station.getId());
            deviceDataTableInfoList.add(tableInfoVO);
        }

        DynamicTableVO result = new DynamicTableVO();
        // 计算合计
        for (JSONObject data : dataList) {
            BigDecimal total = null;
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                if (!"ts".equals(key) && !"total".equals(key)) {
                    if (entry.getValue() instanceof BigDecimal) {
                        if (total == null) {
                            total = new BigDecimal("0");
                        }
                        total = total.add((BigDecimal) entry.getValue());
                    }
                }
            }
            data.put("total", total);
        }

        result.setTableDataList(dataList);
        result.setTableInfo(deviceDataTableInfoList);

        return result;
    }

    @Override
    public List<StationWaterOutletAndInletDataVO> getWaterOutletAndInletReport(String stationType, List<String> stationIdList, Long start, Long end, TenantId tenantId) throws ThingsboardException {
        // 要返回的数据
        List<StationWaterOutletAndInletDataVO> resultList = new ArrayList<>();

        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);

        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 站点MAP
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 供水数据
        Map<String, Map<String, List<String>>> attributeMap = new LinkedHashMap<>();
        for (StationEntity station : stationList) {
            Map<String, List<String>> stationOutletAndInletMap = new HashMap<>();
            List<String> outletAttributes = new ArrayList<>();
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;

            // 出水数据
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }
            List<DeviceFullData> stationDataDetail = new ArrayList<>();
            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                attributeMap.put(station.getId(), null);
            } else {
                stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

                for (DeviceFullData deviceFullData : stationDataDetail) {
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            outletAttributes.add(deviceId + "." + deviceFullData.getProperty());
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }
            }


            stationOutletAndInletMap.put(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue(), outletAttributes);

            // 进水数据
            stationAttr = null;
            List<String> inletAttributes = new ArrayList<>();
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置进水相关的动态属性分组");
                attributeMap.put(station.getId(), null);
            } else {
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            inletAttributes.add(deviceId + "." + deviceFullData.getProperty());
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }

                stationOutletAndInletMap.put(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue(), inletAttributes);
            }

            attributeMap.put(station.getId(), stationOutletAndInletMap);
        }

        // 分组查询站点的进出水信息
        for (Map.Entry<String, Map<String, List<String>>> stationAttributeEntry : attributeMap.entrySet()) {
            String stationId = stationAttributeEntry.getKey();
            StationEntity station = stationMap.get(stationId);
            Map<String, List<String>> attributeDataMap = stationAttributeEntry.getValue();

            // 区分出进水口和出水口数据
            BigDecimal outletTotalFlow = new BigDecimal("0");
            BigDecimal inletTotalFlow = new BigDecimal("0");

            if (attributeDataMap != null) {
                // 出水口
                List<String> outAttributeList = attributeDataMap.get(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue());
                // 进水口
                List<String> inAttributeList = attributeDataMap.get(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue());

                // 查询数据
                List<String> allAttributeList = new ArrayList<>();
                if (outAttributeList != null) {
                    allAttributeList.addAll(outAttributeList);
                }
                if (inAttributeList != null) {
                    allAttributeList.addAll(inAttributeList);
                }

                // 查询数据
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                        obtainDataService.getDeviceData(allAttributeList, start, end, DateUtils.DAY, null, tenantId);

                for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                    String timeKey = entry.getKey();
                    LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                    if (dataMap != null && !dataMap.isEmpty()) {
                        for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                            String key = dataEntry.getKey();
                            BigDecimal flow = dataEntry.getValue();
                            if (flow != null) {
                                // 将数据加到出水总流量
                                if (outAttributeList != null && outAttributeList.contains(key)) {
                                    outletTotalFlow = outletTotalFlow.add(flow);
                                }
                                // 将数据加到进水总流量
                                if (inAttributeList != null && inAttributeList.contains(key)) {
                                    inletTotalFlow = inletTotalFlow.add(flow);
                                }
                            }
                        }
                    }
                }
            }

            // 要返回的数据
            StationWaterOutletAndInletDataVO data = new StationWaterOutletAndInletDataVO();
            data.setStationId(station.getId());
            data.setName(station.getName());
            data.setOutletTotalFlow(outletTotalFlow);
            data.setInletTotalFlow(inletTotalFlow);
            data.setDifferenceTotalFlow(inletTotalFlow.subtract(outletTotalFlow).abs());// 差值绝对值
            if (inletTotalFlow.doubleValue() != 0) {
                data.setDifferenceRate(data.getDifferenceTotalFlow().divide(inletTotalFlow, 4, BigDecimal.ROUND_UP));
            } else {
                data.setDifferenceRate(null);
            }

            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public List<StationWaterSupplyAndEnergyDataVO> getWaterSupplyAndEnergyData(String stationType, Long start, Long end, String queryType, String name, TenantId tenantId) throws ThingsboardException {
        Date lastStartTime = null;
        Date startTime = new Date(start);
        Date endTime = new Date(end);

        // 修复：当查询今日数据时，确保使用当前时间作为结束时间，与getWaterSupplyInfo保持一致
        SimpleDateFormat todayFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date currentTime = new Date();
        if ("day".equals(queryType) && todayFormat.format(startTime).equals(todayFormat.format(currentTime))) {
            // 如果查询的是今天，使用当前时间作为结束时间
            endTime = currentTime;
        }

        Calendar instance = Calendar.getInstance();
        instance.setTime(startTime);

        SimpleDateFormat dateFormat = null;

        // 处理分析类型
        switch (queryType) {
            case "day":
                lastStartTime = new Date(start - (24 * 60 * 60 * 1000));
                dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                break;
            case "month":
                instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
                lastStartTime = instance.getTime();

                dateFormat = new SimpleDateFormat("yyyy-MM");
                break;
            case "year":
                instance.set(Calendar.YEAR, instance.get(Calendar.YEAR) - 1);
                lastStartTime = instance.getTime();

                dateFormat = new SimpleDateFormat("yyyy");
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点列表
        PageData<StationEntity> stationPageResult = stationFeignClient.list(1, 9999, stationType, "");
        List<StationEntity> stationList = stationPageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return new ArrayList<>();
        }
        if (StringUtils.isNotBlank(name)) {
            stationList = stationList.stream().filter(station -> station.getName().contains(name)).collect(Collectors.toList());
        }

        // 站点MAP
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 查询站点数据
        Map<String, Map<String, List<String>>> attributeMap = new LinkedHashMap<>();

        // 查询供水和耗电数据，本期以及上期
        for (StationEntity station : stationList) {
            Map<String, List<String>> stationFlowAndEnergyMap = new HashMap<>();
            List<String> waterOutletAttributeList = new ArrayList<>();

            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;

            // 出水数据
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }
            List<DeviceFullData> stationDataDetail = new ArrayList<>();
            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                attributeMap.put(station.getId(), null);
            } else {
                stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

                // 收集供水量属性
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            waterOutletAttributeList.add(deviceId + "." + deviceFullData.getProperty());
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }
            }

            stationFlowAndEnergyMap.put(DataConstants.DeviceAttrType.TOTAL_FLOW.getValue(), waterOutletAttributeList);

            // 耗电量数据
            List<String> energyAttributes = new ArrayList<>();

            if (stationAttr == null) {
                log.error("水厂未设置进水相关的动态属性分组");
                attributeMap.put(station.getId(), null);
            } else {
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(deviceFullData.getProperty())) {// 耗电量
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            energyAttributes.add(deviceId + "." + deviceFullData.getProperty());
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }

                stationFlowAndEnergyMap.put(DataConstants.DeviceAttrType.ENERGY_IN.getValue(), energyAttributes);
            }

            attributeMap.put(station.getId(), stationFlowAndEnergyMap);
        }

        // 上期时间
        String lastTimeKey = dateFormat.format(lastStartTime);
        // 本期时间
        String thisTimeKey = dateFormat.format(startTime);
        List<StationWaterSupplyAndEnergyDataVO> resultList = new ArrayList<>();
        for (Map.Entry<String, Map<String, List<String>>> stationAttributeEntry : attributeMap.entrySet()) {
            String stationId = stationAttributeEntry.getKey();
            StationEntity station = stationMap.get(stationId);

            // 本期供水量和耗电量
            BigDecimal outletTotalFlow = new BigDecimal("0");
            BigDecimal energyTotal = new BigDecimal("0");
            // 上期供水量和耗电量
            BigDecimal lastTimeOutletTotalFlow = new BigDecimal("0");
            BigDecimal lastTimeEnergyTotal = new BigDecimal("0");

            // 数据属性MAP
            Map<String, List<String>> stationFlowAndEnergyMap = stationAttributeEntry.getValue();
            if (stationFlowAndEnergyMap != null) {
                // 供水量属性列表
                List<String> flowAttributeList = stationFlowAndEnergyMap.get(DataConstants.DeviceAttrType.TOTAL_FLOW.getValue());
                // 耗电量属性列表
                List<String> energyAttributeList = stationFlowAndEnergyMap.get(DataConstants.DeviceAttrType.ENERGY_IN.getValue());

                // 查询数据
                List<String> attributeList = new ArrayList<>();
                if (flowAttributeList != null) {
                    attributeList.addAll(flowAttributeList);
                }
                if (energyAttributeList != null) {
                    attributeList.addAll(energyAttributeList);
                }

                // 执行查询
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                        obtainDataService.getDeviceData(attributeList, lastStartTime.getTime(), endTime.getTime(), queryType, null, tenantId);

                for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                    String timeKey = entry.getKey();
                    LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                    if (dataMap != null) {
                        for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                            if (dataEntry.getValue() == null) {
                                continue;
                            }
                            BigDecimal data = dataEntry.getValue();
                            String key = dataEntry.getKey();
                            // 上期数据统计
                            if (timeKey.equals(lastTimeKey)) {
                                if (energyAttributeList != null && energyAttributeList.contains(key)) {
                                    lastTimeEnergyTotal = lastTimeEnergyTotal.add(data);
                                }
                                if (flowAttributeList != null && flowAttributeList.contains(key)) {
                                    lastTimeOutletTotalFlow = lastTimeOutletTotalFlow.add(data);
                                }
                            }

                            // 本期数据统计 - 修复：统计整个时间范围内的数据
                            try {
                                Date timeKeyDate = dateFormat.parse(timeKey);
                                if (timeKeyDate.compareTo(startTime) >= 0 && timeKeyDate.compareTo(endTime) <= 0) {
                                    if (energyAttributeList != null && energyAttributeList.contains(key)) {
                                        energyTotal = energyTotal.add(data);
                                    }
                                    if (flowAttributeList != null && flowAttributeList.contains(key)) {
                                        outletTotalFlow = outletTotalFlow.add(data);
                                    }
                                }
                            } catch (Exception e) {
                                // 如果日期解析失败，使用原来的字符串匹配方式作为备用
                                if (timeKey.equals(thisTimeKey)) {
                                    if (energyAttributeList != null && energyAttributeList.contains(key)) {
                                        energyTotal = energyTotal.add(data);
                                    }
                                    if (flowAttributeList != null && flowAttributeList.contains(key)) {
                                        outletTotalFlow = outletTotalFlow.add(data);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 计算差值
            BigDecimal energyDifference = energyTotal.subtract(lastTimeEnergyTotal);
            BigDecimal flowDifference = outletTotalFlow.subtract(lastTimeOutletTotalFlow);

            // 设置数据
            StationWaterSupplyAndEnergyDataVO data = new StationWaterSupplyAndEnergyDataVO();
            data.setTime(dateFormat.format(startTime));
            data.setLastTime(dateFormat.format(lastStartTime));
            data.setStationId(station.getId());
            data.setName(station.getName());
            data.setTotalFlow(outletTotalFlow);
            data.setEnergy(energyTotal);
            data.setLastTimeEnergy(lastTimeEnergyTotal);
            data.setLastTimeTotalFlow(lastTimeOutletTotalFlow);

            // 计算本期吨水电耗 - 修正：使用差值计算，保持与前端逻辑一致
            if (flowDifference.doubleValue() != 0 && energyDifference.doubleValue() > 0) {
                data.setUnitConsumption(energyDifference.divide(flowDifference, 2, BigDecimal.ROUND_DOWN));
            } else if (outletTotalFlow.doubleValue() != 0) {
                // 备用方案：如果差值计算不合理，使用原始方法
                data.setUnitConsumption(energyTotal.divide(outletTotalFlow, 2, BigDecimal.ROUND_DOWN));
            }

            // 计算上期吨水电耗
            if (lastTimeOutletTotalFlow.doubleValue() != 0) {
                data.setLastTimeUnitConsumption(lastTimeEnergyTotal.divide(lastTimeOutletTotalFlow, 2, BigDecimal.ROUND_UP));
            }

            // 计算差值
            if (data.getUnitConsumption() != null && data.getLastTimeUnitConsumption() != null) {
                data.setDifferenceValue(data.getUnitConsumption().subtract(data.getLastTimeUnitConsumption()));
            }
            // 计算变化率
            if (data.getDifferenceValue() != null && data.getLastTimeUnitConsumption().doubleValue() != 0) {
                data.setChangeRate(data.getDifferenceValue().divide(data.getLastTimeUnitConsumption(), 4, BigDecimal.ROUND_UP).multiply(new BigDecimal("100")));
            } else {
                data.setChangeRate(BigDecimal.ZERO);
            }

            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public Object getWaterSupplyAndEnergyDataDetail(String stationId, String queryType, Long start, Long end, TenantId tenantId) throws ThingsboardException {
        Date lastStartTime = null;
        Date startTime = new Date(start);
        Date endTime = new Date(end);
        Calendar instance = Calendar.getInstance();
        instance.setTime(startTime);

        SimpleDateFormat dateFormat = null;

        // 处理分析类型
        switch (queryType) {
            case "day":
                lastStartTime = new Date(start - (24 * 60 * 60 * 1000));
                dateFormat = new SimpleDateFormat("yyyy-MM-dd");

                queryType = "1h";
                break;
            case "month":
                instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
                lastStartTime = instance.getTime();

                dateFormat = new SimpleDateFormat("yyyy-MM");
                queryType = "day";
                break;
            case "year":
                instance.set(Calendar.YEAR, instance.get(Calendar.YEAR) - 1);
                lastStartTime = instance.getTime();

                dateFormat = new SimpleDateFormat("yyyy");
                queryType = "month";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点数据
        // 供水量属性列表
        List<String> flowAttributes = new ArrayList<>();

        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;

        // 出水数据
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }
        List<DeviceFullData> stationDataDetail = new ArrayList<>();
        if (stationAttr == null) {
            log.error("水厂未设置供水相关的动态属性分组");
        } else {
            stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            // 收集供水量属性
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        flowAttributes.add(deviceId + "." + deviceFullData.getProperty());
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
        }

        // 耗电量属性列表
        List<String> energyAttributes = new ArrayList<>();

        if (stationAttr == null) {
            log.error("水厂未设置进水相关的动态属性分组");
        } else {
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(deviceFullData.getProperty())) {// 耗电量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        energyAttributes.add(deviceId + "." + deviceFullData.getProperty());
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
        }

        List<String> allAttributeList = new ArrayList<>();
        allAttributeList.addAll(flowAttributes);
        allAttributeList.addAll(energyAttributes);

        // 执行查询
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(allAttributeList, lastStartTime.getTime(), endTime.getTime(), queryType, null, tenantId);

        // 本期供水量、耗电量曲线
        List<LineChartDataVO> flowList = new ArrayList<>();
        List<LineChartDataVO> energyList = new ArrayList<>();
        // 上期供水量、耗电量曲线
        List<LineChartDataVO> lastTimeFlowList = new ArrayList<>();
        List<LineChartDataVO> lastTimeEnergyList = new ArrayList<>();

        // 筛选数据
        String lastTimeKey = dateFormat.format(lastStartTime);
        String thisTimeKey = dateFormat.format(startTime);
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

            // 数据和
            BigDecimal flow = null;
            BigDecimal energy = null;
            if (dataMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    BigDecimal data = dataEntry.getValue();
                    String key = dataEntry.getKey();
                    if (data == null) {
                        continue;
                    }

                    // 供水量数据
                    if (flowAttributes.contains(key)) {
                        if (flow == null) {
                            flow = new BigDecimal("0");
                        }
                        flow = flow.add(data);
                    }

                    // 耗电量数据
                    if (energyAttributes.contains(key)) {
                        if (energy == null) {
                            energy = new BigDecimal("0");
                        }
                        energy = energy.add(data);
                    }
                }
            }

            // 供水量数据
            LineChartDataVO flowDataVO = new LineChartDataVO();
            flowDataVO.setTs(timeKey);
            flowDataVO.setValue(flow);

            // 耗电量数据
            LineChartDataVO energyDataVO = new LineChartDataVO();
            energyDataVO.setTs(timeKey);
            energyDataVO.setValue(energy);

            // 上期数据
            if (timeKey.startsWith(lastTimeKey)) {
                lastTimeFlowList.add(flowDataVO);
                lastTimeEnergyList.add(energyDataVO);
            }
            // 本期数据
            if (timeKey.startsWith(thisTimeKey)) {
                flowList.add(flowDataVO);
                energyList.add(energyDataVO);
            }

        }

        // 计算吨水电耗数据
        List<LineChartDataVO> unitConsumptionList = new ArrayList<>();
        List<LineChartDataVO> lastTimeUnitConsumptionList = new ArrayList<>();
        for (int i = 0; i < flowList.size(); i++) {
            // 本期吨水电耗
            LineChartDataVO flowData = flowList.get(i);
            LineChartDataVO energyData = null;
            if (energyList.size() > i) {
                energyData = energyList.get(i);
            }

            // 计算单耗
            BigDecimal unitConsumption = null;
            if (flowData.getValue() != null && flowData.getValue().doubleValue() != 0 && energyData != null && energyData.getValue() != null) {
                unitConsumption = energyData.getValue().divide(flowData.getValue(), 4, BigDecimal.ROUND_UP);
            }

            LineChartDataVO dataVO = new LineChartDataVO();
            dataVO.setTs(flowData.getTs());
            dataVO.setValue(unitConsumption);
            unitConsumptionList.add(dataVO);
        }

        for (int i = 0; i < lastTimeFlowList.size(); i++) {
            // 上期吨水电耗
            LineChartDataVO lastTimeFlowData = lastTimeFlowList.get(i);
            LineChartDataVO lastTimeEnergyData = null;
            if (lastTimeEnergyList.size() > i) {
                lastTimeEnergyData = lastTimeEnergyList.get(i);
            }

            // 计算单耗
            BigDecimal lastTimeUnitConsumption = null;
            if (lastTimeFlowData.getValue() != null && lastTimeFlowData.getValue().doubleValue() != 0 && lastTimeEnergyData != null && lastTimeEnergyData.getValue() != null) {
                lastTimeUnitConsumption = lastTimeEnergyData.getValue().divide(lastTimeFlowData.getValue(), 4, BigDecimal.ROUND_UP);
            }

            LineChartDataVO lastTimeDataVO = new LineChartDataVO();
            lastTimeDataVO.setTs(lastTimeFlowData.getTs());
            lastTimeDataVO.setValue(lastTimeUnitConsumption);
            lastTimeUnitConsumptionList.add(lastTimeDataVO);
        }

        JSONObject result = new JSONObject();
        result.put("flowList", flowList);
        result.put("energyList", energyList);
        result.put("lastTimeFlowList", lastTimeFlowList);
        result.put("lastTimeEnergyList", lastTimeEnergyList);
        result.put("unitConsumption", unitConsumptionList);
        result.put("lastTimeUnitConsumptionList", lastTimeUnitConsumptionList);
        result.put("time", dateFormat.format(startTime));
        result.put("lastTime", dateFormat.format(lastStartTime));

        return result;
    }

    @Override
    public List<StationWaterOutletAndInletDataVO> getBalanceReport(Long start, Long end, String queryType, String stationId, TenantId tenantId) throws ThingsboardException {
        String exeQueryType = "";
        String suffix = "";
        switch (queryType) {
            case "day":
                exeQueryType = "1h";
                suffix = "时";
                break;
            case "month":
                exeQueryType = "day";
                suffix = "日";
                break;
            case "year":
                exeQueryType = "month";
                suffix = "月";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅允许day、month、year", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据
        List<String> outletAttributes = new ArrayList<>();
        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;

        // 出水数据
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }
        List<DeviceFullData> stationDataDetail = new ArrayList<>();
        if (stationAttr == null) {
            log.error("水厂未设置供水相关的动态属性分组");
        } else {
            stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        outletAttributes.add(deviceId + "." + deviceFullData.getProperty());
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
        }

        // 进水数据
        stationAttr = null;
        List<String> inletAttributes = new ArrayList<>();
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            log.error("水厂未设置进水相关的动态属性分组");
        } else {
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        inletAttributes.add(deviceId + "." + deviceFullData.getProperty());
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
        }

        // 查询数据
        List<String> allAttributeList = new ArrayList<>();
        allAttributeList.addAll(outletAttributes);
        allAttributeList.addAll(inletAttributes);

        // 查询数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(allAttributeList, start, end, exeQueryType, null, tenantId);

        // 数据处理
        List<StationWaterOutletAndInletDataVO> resultList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

            // 数据
            StationWaterOutletAndInletDataVO data = new StationWaterOutletAndInletDataVO();
            data.setStationId(stationId);
            data.setName(Integer.parseInt(StationDataUtil.shortenTimeKey(timeKey, exeQueryType)) + suffix);

            BigDecimal outletFlow = null;
            BigDecimal inletFlow = null;

            // 遍历查询到的数据项
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                BigDecimal value = dataEntry.getValue();
                if (value == null) {
                    continue;
                }
                String key = dataEntry.getKey();
                // 进水流量
                if (inletAttributes.contains(key)) {
                    if (inletFlow == null) {
                        inletFlow = new BigDecimal("0");
                    }
                    inletFlow = inletFlow.add(value);
                }
                // 出水流量
                if (outletAttributes.contains(key)) {
                    if (outletFlow == null) {
                        outletFlow = new BigDecimal("0");
                    }
                    outletFlow = outletFlow.add(value);
                }
            }

            data.setInletTotalFlow(inletFlow);
            data.setOutletTotalFlow(outletFlow);
            // 计算进出口流量差值
            if (inletFlow != null && outletFlow != null) {
                data.setDifferenceTotalFlow(inletFlow.subtract(outletFlow).abs());
                data.setDifferenceRate(BigDecimal.ZERO);
                try {
                    data.setDifferenceRate(data.getDifferenceTotalFlow().divide(outletFlow, 4, BigDecimal.ROUND_UP));
                } catch (Exception e) {
                }
            }

            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public List<StationWaterSupplyAndEnergyDataVO> getEnergyReport(String stationType, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 查询指定站点类型的站点列表
        PageData<StationEntity> stationPageResult = stationFeignClient.list(1, 99999, stationType, "");
        List<StationEntity> stationList = stationPageResult.getData();

        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 站点MAP
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 数据项目对应的站点
        Map<String, List<String>> attrStationMap = new HashMap<>();
        // 供水量数据
        List<String> flowAttributes = new ArrayList<>();
        // 耗电量数据
        List<String> energyAttributes = new ArrayList<>();

        // 获取供水和耗电数据需要查询的数据项并对应到站点（数据项属于哪个站点，方便后续处理数据）
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;

            // 出水数据
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }
            List<DeviceFullData> stationDataDetail = new ArrayList<>();
            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
            } else {
                stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

                // 收集供水量属性
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    // 累计流量
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            String attrKey = deviceId + "." + deviceFullData.getProperty();
                            flowAttributes.add(attrKey);

                            List<String> stationIdList = new ArrayList<>();
                            if (attrStationMap.containsKey(attrKey)) {
                                stationIdList = attrStationMap.get(attrKey);
                            }
                            stationIdList.add(station.getId());
                            attrStationMap.put(attrKey, stationIdList);
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置出水能耗相关的动态属性分组");
            } else {
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    // 耗电量数据
                    if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(deviceFullData.getProperty())) {
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            String attrKey = deviceId + "." + deviceFullData.getProperty();
                            energyAttributes.add(attrKey);

                            List<String> stationIdList = new ArrayList<>();
                            if (attrStationMap.containsKey(attrKey)) {
                                stationIdList = attrStationMap.get(attrKey);
                            }
                            stationIdList.add(station.getId());
                            attrStationMap.put(attrKey, stationIdList);
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }
            }
        }

        // 执行查询
        List<String> allAttributeList = new ArrayList<>();
        allAttributeList.addAll(flowAttributes);
        allAttributeList.addAll(energyAttributes);

        // 执行查询
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(allAttributeList, start, end, queryType, null, tenantId);

        // 初始化返回数据
        Map<String, StationWaterSupplyAndEnergyDataVO> dataVOMap = new LinkedHashMap<>();
        for (StationEntity station : stationList) {
            StationWaterSupplyAndEnergyDataVO dataVO = new StationWaterSupplyAndEnergyDataVO();
            dataVO.setStationId(station.getId());
            dataVO.setName(station.getName());

            dataVOMap.put(station.getId(), dataVO);
        }

        // 统计数据
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            if (dataMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    BigDecimal value = dataEntry.getValue();
                    if (value == null) {
                        continue;
                    }
                    String attrKey = dataEntry.getKey();
                    // 查询该属性属于哪些站点
                    List<String> stationIdList = attrStationMap.get(attrKey);
                    // 查询该站点为供水数据还是能耗数据
                    String flag = "";
                    if (flowAttributes.contains(attrKey)) {
                        flag = DataConstants.DeviceAttrType.TOTAL_FLOW.getValue();
                    }
                    if (energyAttributes.contains(attrKey)) {
                        flag = DataConstants.DeviceAttrType.ENERGY_IN.getValue();
                    }
                    // 遍历站点统计对应站点的数据
                    for (String stationId : stationIdList) {
                        StationWaterSupplyAndEnergyDataVO dataVO = dataVOMap.get(stationId);
                        // 供水量数据累加
                        if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(flag)) {
                            BigDecimal flow = dataVO.getTotalFlow();
                            if (flow == null) {
                                flow = new BigDecimal("0");
                            }
                            flow = flow.add(value);

                            dataVO.setTotalFlow(flow);
                        }
                        // 能耗数据累加
                        if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(flag)) {
                            BigDecimal energy = dataVO.getEnergy();
                            if (energy == null) {
                                energy = new BigDecimal("0");
                            }
                            energy = energy.add(value);

                            dataVO.setEnergy(energy);
                        }

                        dataVOMap.put(stationId, dataVO);
                    }
                }
            }
        }

        // 计算吨水电耗
        List<StationWaterSupplyAndEnergyDataVO> resultList = new ArrayList<>(dataVOMap.values());
        for (StationWaterSupplyAndEnergyDataVO dataVO : resultList) {
            BigDecimal totalFlow = dataVO.getTotalFlow();
            BigDecimal energy = dataVO.getEnergy();

            if (totalFlow != null && totalFlow.doubleValue() != 0 && energy != null) {
                BigDecimal unitConsumption = energy.divide(totalFlow, 4, BigDecimal.ROUND_UP);
                dataVO.setUnitConsumption(unitConsumption);
            }
        }

        return resultList;
    }

    @Override
    public List<WaterSupplyVO> getEnergyDetailReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 查询站点
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 供水量数据
        List<String> flowAttributes = new ArrayList<>();
        // 耗电量数据
        List<String> energyAttributes = new ArrayList<>();

        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;

        // 出水数据
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }
        List<DeviceFullData> stationDataDetail;
        if (stationAttr == null) {
            log.error("水厂未设置供水相关的动态属性分组");
        } else {
            stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);
            // 收集供水量属性
            for (DeviceFullData deviceFullData : stationDataDetail) {
                // 累计流量
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        String attrKey = deviceId + "." + deviceFullData.getProperty();
                        flowAttributes.add(attrKey);
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
                // 耗电量数据
                if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(deviceFullData.getProperty())) {
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        String attrKey = deviceId + "." + deviceFullData.getProperty();
                        energyAttributes.add(attrKey);
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
        }
        // 执行查询
        List<String> allAttributeList = new ArrayList<>();
        allAttributeList.addAll(flowAttributes);
        allAttributeList.addAll(energyAttributes);

        // 执行查询
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(allAttributeList, start, end, queryType, null, tenantId);

        // 数据处理
        List<WaterSupplyVO> resultList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();

            WaterSupplyVO data = new WaterSupplyVO();
            data.setTs(timeKey);

            BigDecimal totalFlow = null;
            BigDecimal energy = null;
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            if (dataMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    String key = dataEntry.getKey();
                    BigDecimal dataValue = dataEntry.getValue();
                    if (dataValue != null) {
                        if (flowAttributes.contains(key)) {
                            if (totalFlow == null) {
                                totalFlow = new BigDecimal("0");
                            }
                            totalFlow = totalFlow.add(dataValue);
                        }
                        if (energyAttributes.contains(key)) {
                            if (energy == null) {
                                energy = new BigDecimal("0");
                            }
                            energy = energy.add(dataValue);
                        }
                    }
                }
            }

            // 计算吨水电耗
            BigDecimal consumption = null;
            if (totalFlow != null && energy != null && totalFlow.doubleValue() != 0) {
                consumption = energy.divide(totalFlow, 2, BigDecimal.ROUND_DOWN);
            }

            data.setTotalFlow(totalFlow);
            data.setEnergy(energy);
            data.setUnitConsumption(consumption);

            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public Object getWaterSupplyFlowReport(String stationId, String queryType, String time, String compareType, TenantId tenantId) throws Exception {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;

        // 出水数据
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }
        if (stationAttr == null) {
            throw new ThingsboardException("站点未设置供水相关属性配置!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

        // 收集供水量属性
        List<String> attributes = new ArrayList<>();
        String unit = "";
        for (DeviceFullData deviceFullData : stationDataDetail) {
            // 累计流量
            if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    String attrKey = deviceId + "." + deviceFullData.getProperty();
                    attributes.add(attrKey);
                    unit = deviceFullData.getUnit();
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }

        return getWaterSupplyDataReport(attributes, queryType, time, compareType, unit, "", tenantId);
    }

    @Override
    public Object getWaterSupplyPressureReport(String stationId, String attributeId, String queryType, String time, String compareType, TenantId tenantId) throws Exception {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        StationAttrEntity stationAttr = stationFeignClient.findAttrById(attributeId);
        if (stationAttr == null) {
            throw new ThingsboardException("要查询的压力点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        List<String> attributes = new ArrayList<>();
        attributes.add(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr());
        String unit = stationAttr.getUnit();

        // 上期开始时间
        Date lastStartTime = null;
        Date lastEndTime = null;

        Map<String, Date> timeRange = StationDataUtil.getTimeRange(time, "day");
        // 本期开始时间
        Date startTime = timeRange.get("start");
        // 本期结束时间
        Date endTime = timeRange.get("end");

        // 处理时间
        Map<String, Date> lastTimeRange = null;
        SimpleDateFormat dataProcessDateFormat = null;
        SimpleDateFormat timeProcessDateFormat = null;
        Calendar instance = Calendar.getInstance();

        // 获取要对比的时间区间
        instance.setTime(startTime);
        if ("1".equals(compareType) || "3".equals(compareType)) {// 同比、定基比
            instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
        }
        if ("2".equals(compareType)) {// 环比
            instance.set(Calendar.DAY_OF_MONTH, instance.get(Calendar.DAY_OF_MONTH) - 1);
        }
        lastTimeRange = StationDataUtil.getTimeRange(instance.getTime(), "day");

        dataProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        timeProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        lastStartTime = lastTimeRange.get("start");
        lastEndTime = lastTimeRange.get("end");

        // 查询本期
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, startTime.getTime(), endTime.getTime(), queryType, null, tenantId);
        // 查询上期
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> lastStationDataMap =
                obtainDataService.getDeviceData(attributes, lastStartTime.getTime(), lastEndTime.getTime(), queryType, null, tenantId);

        // 数据整理
        Map<String, JSONObject> resultDataMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            JSONObject data = new JSONObject();
            String suffix = key.substring(key.length() - 5);
            data.put("ts", suffix);

            // 统计数据
            BigDecimal value = null;
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                if (dataEntry.getValue() != null) {
                    if (value == null) {
                        value = new BigDecimal("0");
                    }
                    value = value.add(dataEntry.getValue());
                }
            }

            data.put(timeProcessDateFormat.format(startTime), value);
            resultDataMap.put(suffix, data);
        }

        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : lastStationDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            String suffix = key.substring(key.length() - 5);
            JSONObject data = resultDataMap.get(suffix);
            if (data == null) {
                data = new JSONObject();
            }
            data.put("ts", suffix);

            // 统计数据
            BigDecimal value = null;
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                if (dataEntry.getValue() != null) {
                    if (value == null) {
                        value = new BigDecimal("0");
                    }
                    value = value.add(dataEntry.getValue());
                }
            }

            data.put(timeProcessDateFormat.format(lastStartTime), value);
            resultDataMap.put(suffix, data);
        }

        // 数据统计。计算变化系数、差值率。统计最大最小值、计算平均值
        String timeKey = timeProcessDateFormat.format(startTime);
        String lastTimeKey = timeProcessDateFormat.format(lastStartTime);
        List<JSONObject> dataList = new ArrayList<>(resultDataMap.values());

        // 本期统计数据
        String timeKeyMaxTs = "";
        BigDecimal timeKeyMax = null;
        String timeKeyMinTs = "";
        BigDecimal timeKeyMin = null;
        BigDecimal timeKeyAvg = null;
        BigDecimal timeKeyTotal = new BigDecimal("0");
        int timeKeyCount = 0;

        // 上期
        String lastTimeKeyMaxTs = "";
        BigDecimal lastTimeKeyMax = null;
        String lastTimeKeyMinTs = "";
        BigDecimal lastTimeKeyMin = null;
        BigDecimal lastTimeKeyAvg = null;
        BigDecimal lastTimeKeyTotal = new BigDecimal("0");
        int lastTimeKeyCount = 0;
        for (int i = 0; i < dataList.size(); i++) {
            JSONObject data = dataList.get(i);
            BigDecimal timeKeyData = data.getBigDecimal(timeKey);
            BigDecimal lastTimeKeyData = data.getBigDecimal(lastTimeKey);

            // 计算的对比数据
            if (timeKeyData != null && lastTimeKeyData != null) {
                // 计算差值率 （本期-上期）/ 上期
                BigDecimal differenceRate = null;
                if (lastTimeKeyData.doubleValue() != 0) {
                    differenceRate = timeKeyData.subtract(lastTimeKeyData).divide(lastTimeKeyData, 2, BigDecimal.ROUND_UP);
                }

                // 计算变化率。（本期+上期）/ 2 / 本期
                BigDecimal changeRate = null;
                if (timeKeyData.doubleValue() != 0) {
                    changeRate = timeKeyData.add(lastTimeKeyData)
                            .divide(new BigDecimal("2"), 2, BigDecimal.ROUND_UP)
                            .divide(timeKeyData, 2, BigDecimal.ROUND_UP);
                }

                data.put("differenceRate", differenceRate);
                data.put("changeRate", changeRate);
            }

            if (timeKeyData != null) {
                if (timeKeyMax == null || timeKeyData.doubleValue() > timeKeyMax.doubleValue()) {
                    timeKeyMax = timeKeyData;
                    timeKeyMaxTs = data.getString("ts");
                }

                if (timeKeyMin == null || timeKeyData.doubleValue() < timeKeyMin.doubleValue()) {
                    timeKeyMin = timeKeyData;
                    timeKeyMinTs = data.getString("ts");
                }
                timeKeyCount++;
                timeKeyTotal = timeKeyTotal.add(timeKeyData);
            }

            if (lastTimeKeyData != null) {
                if (lastTimeKeyMax == null || lastTimeKeyData.doubleValue() > lastTimeKeyMax.doubleValue()) {
                    lastTimeKeyMax = lastTimeKeyData;
                    lastTimeKeyMaxTs = data.getString("ts");
                }

                if (lastTimeKeyMin == null || lastTimeKeyData.doubleValue() < lastTimeKeyMin.doubleValue()) {
                    lastTimeKeyMin = lastTimeKeyData;
                    lastTimeKeyMinTs = data.getString("ts");
                }
                lastTimeKeyCount++;
                lastTimeKeyTotal = lastTimeKeyTotal.add(lastTimeKeyData);
            }
            data.put("ts", data.getString("ts"));
        }

        // 统计结果汇总
        List<JSONObject> countInfoList = new ArrayList<>();
        JSONObject timeKeyCountInfo = new JSONObject();
        timeKeyCountInfo.put("ts", timeKey);
        timeKeyCountInfo.put("max", timeKeyMax);
        timeKeyCountInfo.put("maxTs", timeKeyMaxTs);
        timeKeyCountInfo.put("min", timeKeyMin);
        timeKeyCountInfo.put("minTs", timeKeyMinTs);
        if (timeKeyCount != 0) {
            timeKeyAvg = timeKeyTotal.divide(BigDecimal.valueOf(timeKeyCount), 2, BigDecimal.ROUND_DOWN);
        }
        timeKeyCountInfo.put("avg", timeKeyAvg);

        JSONObject lastTimeKeyCountInfo = new JSONObject();
        lastTimeKeyCountInfo.put("ts", lastTimeKey);
        lastTimeKeyCountInfo.put("max", lastTimeKeyMax);
        lastTimeKeyCountInfo.put("maxTs", lastTimeKeyMaxTs);
        lastTimeKeyCountInfo.put("min", lastTimeKeyMin);
        lastTimeKeyCountInfo.put("minTs", lastTimeKeyMinTs);
        if (lastTimeKeyCount != 0) {
            lastTimeKeyAvg = lastTimeKeyTotal.divide(BigDecimal.valueOf(lastTimeKeyCount), 2, BigDecimal.ROUND_DOWN);
        }
        lastTimeKeyCountInfo.put("avg", lastTimeKeyAvg);
        countInfoList.add(timeKeyCountInfo);
        countInfoList.add(lastTimeKeyCountInfo);


        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();

        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList(
                "ts:时间",
                timeKey + ":" + timeKey + ":MPa",
                lastTimeKey + ":" + lastTimeKey + ":MPa",
                "differenceRate:差值率:%",
                "changeRate:变化系数"
        ));

        DynamicTableVO dynamicTableVO = new DynamicTableVO();
        dynamicTableVO.setTableDataList(dataList);
        dynamicTableVO.setTableInfo(deviceDataTableInfoList);

        JSONObject result = new JSONObject();
        result.put("baseTable", dynamicTableVO);
        result.put("countTable", countInfoList);

        return result;
    }

    @Override
    public Object getWaterSupplyQualityReport(String stationId, String attrType, String queryType, String time, String compareType, TenantId tenantId) throws Exception {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;

        // 出水数据
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }
        if (stationAttr == null) {
            throw new ThingsboardException("站点未设置供水相关属性配置!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

        // 收集供水量属性
        List<String> attributes = new ArrayList<>();
        String unit = "";
        for (DeviceFullData deviceFullData : stationDataDetail) {
            // 累计流量
            if (attrType.equals(deviceFullData.getProperty())) {
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    String attrKey = deviceId + "." + deviceFullData.getProperty();
                    attributes.add(attrKey);
                    unit = deviceFullData.getUnit();
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }

        // 上期开始时间
        Date lastStartTime = null;
        Date lastEndTime = null;

        Map<String, Date> timeRange = StationDataUtil.getTimeRange(time, "day");
        // 本期开始时间
        Date startTime = timeRange.get("start");
        // 本期结束时间
        Date endTime = timeRange.get("end");

        // 处理时间
        Map<String, Date> lastTimeRange = null;
        SimpleDateFormat dataProcessDateFormat = null;
        SimpleDateFormat timeProcessDateFormat = null;
        Calendar instance = Calendar.getInstance();

        // 获取要对比的时间区间
        instance.setTime(startTime);
        if ("1".equals(compareType) || "3".equals(compareType)) {// 同比、定基比
            instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
        }
        if ("2".equals(compareType)) {// 环比
            instance.set(Calendar.DAY_OF_MONTH, instance.get(Calendar.DAY_OF_MONTH) - 1);
        }
        lastTimeRange = StationDataUtil.getTimeRange(instance.getTime(), "day");

        dataProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        timeProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        lastStartTime = lastTimeRange.get("start");
        lastEndTime = lastTimeRange.get("end");

        // 查询本期
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, startTime.getTime(), endTime.getTime(), queryType, null, tenantId);
        // 查询上期
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> lastStationDataMap =
                obtainDataService.getDeviceData(attributes, lastStartTime.getTime(), lastEndTime.getTime(), queryType, null, tenantId);

        // 数据整理
        Map<String, JSONObject> resultDataMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            JSONObject data = new JSONObject();
            String suffix = key.substring(key.length() - 5);
            data.put("ts", suffix);

            // 统计数据
            BigDecimal value = null;
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                if (dataEntry.getValue() != null) {
                    if (value == null) {
                        value = new BigDecimal("0");
                    }
                    value = value.add(dataEntry.getValue());
                }
            }

            data.put(timeProcessDateFormat.format(startTime), value);
            resultDataMap.put(suffix, data);
        }

        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : lastStationDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            String suffix = key.substring(key.length() - 5);
            JSONObject data = resultDataMap.get(suffix);
            if (data == null) {
                data = new JSONObject();
            }
            data.put("ts", suffix);

            // 统计数据
            BigDecimal value = null;
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                if (dataEntry.getValue() != null) {
                    if (value == null) {
                        value = new BigDecimal("0");
                    }
                    value = value.add(dataEntry.getValue());
                }
            }

            data.put(timeProcessDateFormat.format(lastStartTime), value);
            resultDataMap.put(suffix, data);
        }

        // 数据统计。计算变化系数、差值率。统计最大最小值、计算平均值
        String timeKey = timeProcessDateFormat.format(startTime);
        String lastTimeKey = timeProcessDateFormat.format(lastStartTime);
        List<JSONObject> dataList = new ArrayList<>(resultDataMap.values());

        // 本期统计数据
        String timeKeyMaxTs = "";
        BigDecimal timeKeyMax = null;
        String timeKeyMinTs = "";
        BigDecimal timeKeyMin = null;
        BigDecimal timeKeyAvg = null;
        BigDecimal timeKeyTotal = new BigDecimal("0");
        int timeKeyCount = 0;

        // 上期
        String lastTimeKeyMaxTs = "";
        BigDecimal lastTimeKeyMax = null;
        String lastTimeKeyMinTs = "";
        BigDecimal lastTimeKeyMin = null;
        BigDecimal lastTimeKeyAvg = null;
        BigDecimal lastTimeKeyTotal = new BigDecimal("0");
        int lastTimeKeyCount = 0;
        for (int i = 0; i < dataList.size(); i++) {
            JSONObject data = dataList.get(i);
            BigDecimal timeKeyData = data.getBigDecimal(timeKey);
            BigDecimal lastTimeKeyData = data.getBigDecimal(lastTimeKey);

            // 计算的对比数据
            if (timeKeyData != null && lastTimeKeyData != null) {
                // 计算差值率 （本期-上期）/ 上期
                BigDecimal differenceRate = null;
                if (lastTimeKeyData.doubleValue() != 0) {
                    differenceRate = timeKeyData.subtract(lastTimeKeyData).divide(lastTimeKeyData, 2, BigDecimal.ROUND_UP);
                }

                // 计算变化率。（本期+上期）/ 2 / 本期
                BigDecimal changeRate = null;
                if (timeKeyData.doubleValue() != 0) {
                    changeRate = timeKeyData.add(lastTimeKeyData)
                            .divide(new BigDecimal("2"), 2, BigDecimal.ROUND_UP)
                            .divide(timeKeyData, 2, BigDecimal.ROUND_UP);
                }

                data.put("differenceRate", differenceRate);
                data.put("changeRate", changeRate);
            }

            if (timeKeyData != null) {
                if (timeKeyMax == null || timeKeyData.doubleValue() > timeKeyMax.doubleValue()) {
                    timeKeyMax = timeKeyData;
                    timeKeyMaxTs = data.getString("ts");
                }

                if (timeKeyMin == null || timeKeyData.doubleValue() < timeKeyMin.doubleValue()) {
                    timeKeyMin = timeKeyData;
                    timeKeyMinTs = data.getString("ts");
                }
                timeKeyCount++;
                timeKeyTotal = timeKeyTotal.add(timeKeyData);
            }

            if (lastTimeKeyData != null) {
                if (lastTimeKeyMax == null || lastTimeKeyData.doubleValue() > lastTimeKeyMax.doubleValue()) {
                    lastTimeKeyMax = lastTimeKeyData;
                    lastTimeKeyMaxTs = data.getString("ts");
                }

                if (lastTimeKeyMin == null || lastTimeKeyData.doubleValue() < lastTimeKeyMin.doubleValue()) {
                    lastTimeKeyMin = lastTimeKeyData;
                    lastTimeKeyMinTs = data.getString("ts");
                }
                lastTimeKeyCount++;
                lastTimeKeyTotal = lastTimeKeyTotal.add(lastTimeKeyData);
            }
            data.put("ts", data.getString("ts"));
        }

        // 统计结果汇总
        List<JSONObject> countInfoList = new ArrayList<>();
        JSONObject timeKeyCountInfo = new JSONObject();
        timeKeyCountInfo.put("ts", timeKey);
        timeKeyCountInfo.put("max", timeKeyMax);
        timeKeyCountInfo.put("maxTs", timeKeyMaxTs);
        timeKeyCountInfo.put("min", timeKeyMin);
        timeKeyCountInfo.put("minTs", timeKeyMinTs);
        if (timeKeyCount != 0) {
            timeKeyAvg = timeKeyTotal.divide(BigDecimal.valueOf(timeKeyCount), 2, BigDecimal.ROUND_DOWN);
        }
        timeKeyCountInfo.put("avg", timeKeyAvg);

        JSONObject lastTimeKeyCountInfo = new JSONObject();
        lastTimeKeyCountInfo.put("ts", lastTimeKey);
        lastTimeKeyCountInfo.put("max", lastTimeKeyMax);
        lastTimeKeyCountInfo.put("maxTs", lastTimeKeyMaxTs);
        lastTimeKeyCountInfo.put("min", lastTimeKeyMin);
        lastTimeKeyCountInfo.put("minTs", lastTimeKeyMinTs);
        if (lastTimeKeyCount != 0) {
            lastTimeKeyAvg = lastTimeKeyTotal.divide(BigDecimal.valueOf(lastTimeKeyCount), 2, BigDecimal.ROUND_DOWN);
        }
        lastTimeKeyCountInfo.put("avg", lastTimeKeyAvg);
        countInfoList.add(timeKeyCountInfo);
        countInfoList.add(lastTimeKeyCountInfo);


        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();

        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList(
                "ts:时间",
                timeKey + ":" + timeKey + ":" + unit,
                lastTimeKey + ":" + lastTimeKey + ":" + unit,
                "differenceRate:差值率:%",
                "changeRate:变化系数"
        ));

        DynamicTableVO dynamicTableVO = new DynamicTableVO();
        dynamicTableVO.setTableDataList(dataList);
        dynamicTableVO.setTableInfo(deviceDataTableInfoList);

        JSONObject result = new JSONObject();
        result.put("baseTable", dynamicTableVO);
        result.put("countTable", countInfoList);

        return result;
    }

    @Override
    public DynamicTableVO getWaterPlantFlowReport(String stationType, List<String> stationIdList, String queryType, String time, TenantId tenantId) throws Exception {
        String exeQueryType = "";
        String suffix = "";
        switch (queryType) {
            case "day":
                exeQueryType = "1h";
                suffix = "时";
                break;
            case "month":
                exeQueryType = "day";
                suffix = "日";
                break;
            case "year":
                exeQueryType = "month";
                suffix = "月";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅允许day、month、year", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);
        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 属性对应的站点列表
        Map<String, List<String>> attrStationMap = new HashMap<>();
        // 站点的进出水属性列表
        Map<String, Map<String, List<String>>> stationAttributesMap = new HashMap<>();
        // 获取到每个站点的出水流量和累计流量
        for (StationEntity station : stationList) {
            Map<String, List<String>> stationAttributeListMap = new HashMap<>();
            List<String> outletFlowAttributes = new ArrayList<>();
            List<String> inletFlowAttributes = new ArrayList<>();
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;

            // 出水数据
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }
            List<DeviceFullData> stationDataDetail = new ArrayList<>();
            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
            } else {
                stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

                // 收集供水量属性
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    // 累计流量
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            String attrKey = deviceId + "." + deviceFullData.getProperty();
                            outletFlowAttributes.add(attrKey);

                            List<String> stationIds = new ArrayList<>();
                            if (attrStationMap.containsKey(attrKey)) {
                                stationIds = attrStationMap.get(attrKey);
                            }
                            stationIds.add(station.getId());
                            attrStationMap.put(attrKey, stationIds);
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }
            }

            stationAttributeListMap.put(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue(), outletFlowAttributes);

            // 进水数据
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }
            stationDataDetail = new ArrayList<>();
            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
            } else {
                stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

                // 收集进水量属性
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    // 累计流量
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            String attrKey = deviceId + "." + deviceFullData.getProperty();
                            inletFlowAttributes.add(attrKey);

                            List<String> stationIds = new ArrayList<>();
                            if (attrStationMap.containsKey(attrKey)) {
                                stationIds = attrStationMap.get(attrKey);
                            }
                            stationIds.add(station.getId());
                            attrStationMap.put(attrKey, stationIds);
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }
            }
            stationAttributeListMap.put(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue(), inletFlowAttributes);
            // 放入站点的属性map
            stationAttributesMap.put(station.getId(), stationAttributeListMap);
        }

        // 数据查询
        Map<String, Date> timeRange = StationDataUtil.getTimeRange(time, queryType);
        Date start = timeRange.get("start");
        Date end = timeRange.get("end");
        List<String> allAttributeList = new ArrayList<>(attrStationMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(allAttributeList, start.getTime(), end.getTime(), exeQueryType, null, tenantId);

        // 数据分组
        List<JSONObject> dataList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

            // 数据
            JSONObject data = new JSONObject();
            data.put("ts", timeKey);
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                String dataKey = dataEntry.getKey();
                BigDecimal value = dataEntry.getValue();
                if (value == null) {
                    continue;
                }
                List<String> stationIds = attrStationMap.get(dataKey);

                for (String stationId : stationIds) {
                    Map<String, List<String>> stationAttrMap = stationAttributesMap.get(stationId);
                    List<String> outletAttributes = stationAttrMap.get(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue());
                    List<String> inletAttributes = stationAttrMap.get(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue());

                    // 校验是出水还是进水
                    if (outletAttributes.contains(dataKey)) {// 出水
                        BigDecimal outletValue = data.getBigDecimal(stationId + "--outlet");
                        if (outletValue == null) {
                            outletValue = new BigDecimal("0");
                        }
                        outletValue = outletValue.add(value);
                        data.put(stationId + "--outlet", outletValue);
                    }
                    if (inletAttributes.contains(dataKey)) {// 进水
                        BigDecimal inletValue = data.getBigDecimal(stationId + "--inlet");
                        if (inletValue == null) {
                            inletValue = new BigDecimal("0");
                        }
                        inletValue = inletValue.add(value);
                        data.put(stationId + "--inlet", inletValue);
                    }
                }
            }
            dataList.add(data);
        }

        // 统计数据
        Map<String, JSONObject> countMap = new LinkedHashMap<>();
        countMap.put("max", new JSONObject());
        countMap.put("maxTs", new JSONObject());
        countMap.put("min", new JSONObject());
        countMap.put("minTs", new JSONObject());
        countMap.put("avg", new JSONObject());
        countMap.put("total", new JSONObject());

        for (StationEntity station : stationList) {
            String stationId = station.getId();
            // 出水统计数据
            BigDecimal outletMax = null;
            BigDecimal outletMin = null;
            String outletMaxTs = null;
            String outletMinTs = null;
            BigDecimal outletTotal = new BigDecimal("0");
            BigDecimal outletAvg = new BigDecimal("0");
            int outletCount = 0;
            // 进水统计数据
            BigDecimal inletMax = null;
            BigDecimal inletMin = null;
            String inletMaxTs = null;
            String inletMinTs = null;
            BigDecimal inletTotal = new BigDecimal("0");
            BigDecimal inletAvg = new BigDecimal("0");
            int inletCount = 0;
            for (JSONObject data : dataList) {
                BigDecimal inletValue = data.getBigDecimal(stationId + "--inlet");
                BigDecimal outletValue = data.getBigDecimal(stationId + "--outlet");
                if (outletValue != null) {
                    if (outletMax == null || outletMax.doubleValue() < outletValue.doubleValue()) {
                        outletMax = outletValue;
                        outletMaxTs = data.getString("ts");
                    }
                    if (outletMin == null || outletMin.doubleValue() > outletValue.doubleValue()) {
                        outletMin = outletValue;
                        outletMinTs = data.getString("ts");
                    }
                    outletTotal = outletTotal.add(outletValue);
                    outletCount++;
                }
                if (inletValue != null) {
                    if (inletMax == null || inletMax.doubleValue() < inletValue.doubleValue()) {
                        inletMax = inletValue;
                        inletMaxTs = data.getString("ts");
                    }
                    if (inletMin == null || inletMin.doubleValue() > inletValue.doubleValue()) {
                        inletMin = inletValue;
                        inletMinTs = data.getString("ts");
                    }
                    inletTotal = inletTotal.add(inletValue);
                    inletCount++;
                }
            }
            // 计算平均值
            if (outletCount != 0) {
                outletAvg = outletTotal.divide(BigDecimal.valueOf(outletCount), 2, BigDecimal.ROUND_DOWN);
            }
            if (inletCount != 0) {
                inletAvg = inletTotal.divide(BigDecimal.valueOf(inletCount), 2, BigDecimal.ROUND_DOWN);
            }

            JSONObject maxObj = countMap.get("max");
            maxObj.put(stationId + "--outlet", outletMax);
            maxObj.put(stationId + "--inlet", inletMax);
            countMap.put("max", maxObj);

            JSONObject maxTsObj = countMap.get("maxTs");
            maxTsObj.put(stationId + "--outlet", outletMaxTs);
            maxTsObj.put(stationId + "--inlet", inletMaxTs);
            countMap.put("maxTs", maxTsObj);

            JSONObject minObj = countMap.get("min");
            minObj.put(stationId + "--outlet", outletMin);
            minObj.put(stationId + "--inlet", inletMin);
            countMap.put("min", minObj);

            JSONObject minTsObj = countMap.get("minTs");
            minTsObj.put(stationId + "--outlet", outletMinTs);
            minTsObj.put(stationId + "--inlet", inletMinTs);
            countMap.put("minTs", minTsObj);

            JSONObject avgObj = countMap.get("avg");
            avgObj.put(stationId + "--outlet", outletAvg);
            avgObj.put(stationId + "--inlet", inletAvg);
            countMap.put("avg", avgObj);

            JSONObject totalObj = countMap.get("total");
            totalObj.put(stationId + "--outlet", outletTotal);
            totalObj.put(stationId + "--inlet", inletTotal);
            countMap.put("total", totalObj);
        }

        for (Map.Entry<String, JSONObject> countObj : countMap.entrySet()) {
            String key = countObj.getKey();
            JSONObject data = countObj.getValue();
            String ts = "";
            switch (key) {
                case "max":
                    ts = "最大值";
                    break;
                case "maxTs":
                    ts = "最大值时间";
                    break;
                case "min":
                    ts = "最小值";
                    break;
                case "minTs":
                    ts = "最小值时间";
                    break;
                case "avg":
                    ts = "平均值";
                    break;
                case "total":
                    ts = "合计";
                    break;
            }
            data.put("ts", ts);
            dataList.add(data);
        }


        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (StationEntity station : stationList) {
            String stationId = station.getId();
            DeviceDataTableInfoVO outletVO = new DeviceDataTableInfoVO();
            outletVO.setColumnName(station.getName() + "--出厂累计流量");
            outletVO.setColumnValue(stationId + "--outlet");
            outletVO.setUnit("m³");
            deviceDataTableInfoList.add(outletVO);

            DeviceDataTableInfoVO inletVO = new DeviceDataTableInfoVO();
            inletVO.setColumnName(station.getName() + "--进厂累计流量");
            inletVO.setColumnValue(stationId + "--inlet");
            inletVO.setUnit("m³");
            deviceDataTableInfoList.add(inletVO);
        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public DynamicTableVO getWaterPlantProductionReport(String stationId, String groupType, String queryType, String time, TenantId tenantId) throws Exception {
        String exeQueryType = "";
        String suffix = "";
        switch (queryType) {
            case "day":
                exeQueryType = "1h";
                suffix = "时";
                break;
            case "month":
                exeQueryType = "day";
                suffix = "日";
                break;
            case "year":
                exeQueryType = "month";
                suffix = "月";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅允许day、month、year", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        Map<String, Date> timeRange = StationDataUtil.getTimeRange(time, queryType);
        Date start = timeRange.get("start");
        Date end = timeRange.get("end");
        // 查询站点
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询分组下的数据项
        List<StationAttrEntity> stationAttrList = stationFeignClient.getAttrList(stationId, groupType);
        if (stationAttrList == null) {
            throw new ThingsboardException("要查询的数据分组下没有数据点!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Map<String, StationAttrEntity> stationAttrMap = new LinkedHashMap<>();

        List<String> attributes = new ArrayList<>();
        for (StationAttrEntity stationAttr : stationAttrList) {
            String attribute = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr();
            attributes.add(attribute);

            stationAttrMap.put(attribute, stationAttr);
        }

        // 执行查询
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, start.getTime(), end.getTime(), exeQueryType, null, tenantId);

        // 数据处理
        List<JSONObject> dataList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

            JSONObject data = new JSONObject();
            data.put("ts", StationDataUtil.shortenTimeKey(timeKey, queryType) + suffix);
            if (dataMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    String dataKey = dataEntry.getKey();
                    data.put(dataKey, dataEntry.getValue());
                }
            }
            dataList.add(data);
        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (Map.Entry<String, StationAttrEntity> stationAttrEntry : stationAttrMap.entrySet()) {
            String key = stationAttrEntry.getKey();
            StationAttrEntity stationAttr = stationAttrEntry.getValue();
            DeviceDataTableInfoVO infoVO = new DeviceDataTableInfoVO();
            infoVO.setColumnName(stationAttr.getName());
            infoVO.setUnit(stationAttr.getUnit());
            infoVO.setColumnValue(key);

            deviceDataTableInfoList.add(infoVO);
        }
        DynamicTableVO result = new DynamicTableVO();
        result.setTableDataList(dataList);
        result.setTableInfo(deviceDataTableInfoList);

        return result;
    }

    @Override
    public List<WaterSupplyVO> getWaterSupplyOverview(String stationType, Long start, Long end, String queryType, String name, TenantId tenantId) throws ThingsboardException {
        // 查询站点列表
        PageData<StationEntity> stationPageResult = stationFeignClient.list(1, 9999, stationType, "");
        List<StationEntity> stationList = stationPageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return new ArrayList<>();
        }
        if (StringUtils.isNotBlank(name)) {
            stationList = stationList.stream().filter(station -> station.getName().contains(name)).collect(Collectors.toList());
        }

        // 站点MAP
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 数据项目对应的站点
        Map<String, List<String>> attrStationMap = new HashMap<>();
        // 供水量数据
        List<String> flowAttributes = new ArrayList<>();
        // 耗电量数据
        List<String> energyAttributes = new ArrayList<>();
        // 运行时长
        List<String> runtimeAttributes = new ArrayList<>();

        List<String> attributes = new ArrayList<>();
        // 获取供水和耗电数据需要查询的数据项并对应到站点（数据项属于哪个站点，方便后续处理数据）
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;

            // 出水数据
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }
            List<DeviceFullData> stationDataDetail = new ArrayList<>();
            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
            } else {
                stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

                // 收集供水量属性
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    // 累计流量
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            String attrKey = deviceId + "." + deviceFullData.getProperty();
                            flowAttributes.add(attrKey);

                            List<String> stationIdList = new ArrayList<>();
                            if (attrStationMap.containsKey(attrKey)) {
                                stationIdList = attrStationMap.get(attrKey);
                            }
                            stationIdList.add(station.getId());
                            attrStationMap.put(attrKey, stationIdList);
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                    // 耗电量数据
                    if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(deviceFullData.getProperty())) {
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            String attrKey = deviceId + "." + deviceFullData.getProperty();
                            energyAttributes.add(attrKey);

                            List<String> stationIdList = new ArrayList<>();
                            if (attrStationMap.containsKey(attrKey)) {
                                stationIdList = attrStationMap.get(attrKey);
                            }
                            stationIdList.add(station.getId());
                            attrStationMap.put(attrKey, stationIdList);
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                    // 运行时长
                    if (DataConstants.DeviceAttrType.RUNTIME.getValue().equals(deviceFullData.getProperty())) {
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            String attrKey = deviceId + "." + deviceFullData.getProperty();
                            runtimeAttributes.add(attrKey);

                            List<String> stationIdList = new ArrayList<>();
                            if (attrStationMap.containsKey(attrKey)) {
                                stationIdList = attrStationMap.get(attrKey);
                            }
                            stationIdList.add(station.getId());
                            attrStationMap.put(attrKey, stationIdList);
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }
            }

        }

        // 执行查询
        attributes.addAll(flowAttributes);
        attributes.addAll(energyAttributes);
        attributes.addAll(runtimeAttributes);

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, start, end, queryType, null, tenantId);

        // 初始化返回数据
        Map<String, WaterSupplyVO> dataVOMap = new LinkedHashMap<>();
        for (StationEntity station : stationList) {
            WaterSupplyVO vo = new WaterSupplyVO();
            vo.setStationId(station.getId());
            vo.setName(station.getName());

            dataVOMap.put(station.getId(), vo);
        }

        // 处理数据
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            if (dataMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    BigDecimal value = dataEntry.getValue();
                    if (value == null) {
                        continue;
                    }
                    String attrKey = dataEntry.getKey();
                    // 查询该属性属于哪些站点
                    List<String> stationIdList = attrStationMap.get(attrKey);
                    // 查询该站点为供水数据还是能耗数据
                    String flag = "";
                    if (flowAttributes.contains(attrKey)) {
                        flag = DataConstants.DeviceAttrType.TOTAL_FLOW.getValue();
                    }
                    if (energyAttributes.contains(attrKey)) {
                        flag = DataConstants.DeviceAttrType.ENERGY_IN.getValue();
                    }
                    if (runtimeAttributes.contains(attrKey)) {
                        flag = DataConstants.DeviceAttrType.RUNTIME.getValue();
                    }
                    // 遍历站点统计对应站点的数据
                    for (String stationId : stationIdList) {
                        WaterSupplyVO dataVO = dataVOMap.get(stationId);
                        // 供水量数据累加
                        if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(flag)) {
                            BigDecimal flow = dataVO.getTotalFlow();
                            if (flow == null) {
                                flow = new BigDecimal("0");
                            }
                            flow = flow.add(value);

                            dataVO.setTotalFlow(flow);
                        }
                        // 能耗数据累加
                        if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(flag)) {
                            BigDecimal energy = dataVO.getEnergy();
                            if (energy == null) {
                                energy = new BigDecimal("0");
                            }
                            energy = energy.add(value);

                            dataVO.setEnergy(energy);
                        }
                        // 运行时间
                        if (DataConstants.DeviceAttrType.RUNTIME.getValue().equals(flag)) {
                            BigDecimal runtme = dataVO.getRuntime();
                            if (runtme == null) {
                                runtme = new BigDecimal("0");
                            }
                            runtme = runtme.add(value);

                            dataVO.setRuntime(runtme);
                        }

                        dataVOMap.put(stationId, dataVO);
                    }
                }
            }
        }

        // 计算吨水单耗
        List<WaterSupplyVO> resultList = new ArrayList<>(dataVOMap.values());
        for (WaterSupplyVO dataVO : resultList) {
            BigDecimal totalFlow = dataVO.getTotalFlow();
            BigDecimal energy = dataVO.getEnergy();

            if (totalFlow != null && totalFlow.doubleValue() != 0 && energy != null) {
                BigDecimal unitConsumption = energy.divide(totalFlow, 4, BigDecimal.ROUND_UP);
                dataVO.setUnitConsumption(unitConsumption);
            }
        }

        return resultList;
    }

    @Override
    public Object getWaterSupplyOverviewTrend(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        switch (queryType) {
            case "day":
                queryType = "1h";
                break;
            case "month":
                queryType = "day";
                break;
            case "year":
                queryType = "month";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅允许day、month、year", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点
        StationEntity station = stationFeignClient.get(stationId);
        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;

        // 供水量数据
        List<String> flowAttributes = new ArrayList<>();
        // 耗电量数据
        List<String> energyAttributes = new ArrayList<>();

        // 出水数据
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }
        List<DeviceFullData> stationDataDetail;
        if (stationAttr == null) {
            log.error("水厂未设置供水相关的动态属性分组");
        } else {
            stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            // 收集供水量属性
            for (DeviceFullData deviceFullData : stationDataDetail) {
                // 累计流量
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        String attrKey = deviceId + "." + deviceFullData.getProperty();
                        flowAttributes.add(attrKey);
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
                // 耗电量数据
                if (DataConstants.DeviceAttrType.ENERGY_IN.getValue().equals(deviceFullData.getProperty())) {
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        String attrKey = deviceId + "." + deviceFullData.getProperty();
                        energyAttributes.add(attrKey);
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
        }
        List<String> attributes = new ArrayList<>();
        // 执行查询
        attributes.addAll(flowAttributes);
        attributes.addAll(energyAttributes);

        if (attributes.isEmpty()) {
            throw new ThingsboardException("未找到要查询的数据属性!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, start, end, queryType, null, tenantId);

        // 处理数据
        List<LineChartDataVO> flowList = new ArrayList<>();
        List<LineChartDataVO> energyList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();
            LineChartDataVO flow = new LineChartDataVO();
            flow.setTs(timeKey);
            LineChartDataVO energy = new LineChartDataVO();
            energy.setTs(timeKey);
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            BigDecimal flowData = null;
            BigDecimal energyData = null;
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                BigDecimal dataValue = dataEntry.getValue();
                if (dataValue == null) {
                    continue;
                }
                String key = dataEntry.getKey();
                if (flowAttributes.contains(key)) {
                    if (flowData == null) {
                        flowData = new BigDecimal("0");
                    }
                    flowData = flowData.add(dataValue);
                }
                if (energyAttributes.contains(key)) {
                    if (energyData == null) {
                        energyData = new BigDecimal("0");
                    }
                    energyData = energyData.add(dataValue);
                }
            }
            flow.setValue(flowData);
            energy.setValue(energyData);

            flowList.add(flow);
            energyList.add(energy);
        }

        JSONObject result = new JSONObject();
        result.put("flowData", flowList);
        result.put("energyData", energyList);

        return result;
    }

    @Override
    public WaterInfoVO getWaterInfo(String stationId, TenantId tenantId) throws ThingsboardException {
        // 查询站点
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat tsDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:ss:mm");

        // 处理时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        // 月时间
        Date monthStart = instance.getTime();
        Date monthEnd = new Date();
        // 昨日时间
        Date yesterdayStart = new Date(monthEnd.getTime() - (24 * 60 * 60 * 1000));
        // 检查是否为月初的第一天, 若为第一天需要额外查询上一个月的最后一天的数据
        boolean isMonthStart = false;
        if (dateFormat.format(monthEnd).endsWith("-01")) {
            isMonthStart = true;
            yesterdayStart = new Date(monthStart.getTime() - (24 * 60 * 60 * 1000));
        }

        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        WaterInfoVO vo = new WaterInfoVO();
        vo.setStationId(station.getId());
        vo.setName(station.getName());

        // 供水数据
        if (stationAttr != null) {
            long ts = -1;
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            List<String> attributes = new ArrayList<>();
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    attributes = Collections.singletonList(deviceId + "." + deviceFullData.getProperty());
                }
                if (deviceFullData.getCollectionTime() > ts) {
                    ts = deviceFullData.getCollectionTime();
                }
            }

            try {
                if (isMonthStart) {
                    totalFlowData = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), monthEnd.getTime(), DateUtils.DAY, null, tenantId);
                } else {
                    totalFlowData = obtainDataService.getDeviceData(attributes, monthStart.getTime(), monthEnd.getTime(), DateUtils.DAY, null, tenantId);
                }
            } catch (ThingsboardException e) {
                log.error("[查询站点数据异常] 查询数据异常");
            }

            // 累加本月数据
            if (totalFlowData == null) {
                log.error("[查询站点数据异常] 查询数据异常");
            }
            BigDecimal monthTotalFlow = new BigDecimal("0");
            BigDecimal yesterdayTotalFlow = new BigDecimal("0");
            BigDecimal todayTotalFlow = new BigDecimal("0");
            if (isMonthStart) {// 月初第一天
                // 昨日数据
                LinkedHashMap<String, BigDecimal> yesterdayDataMap = totalFlowData.get(dateFormat.format(yesterdayStart.getTime()));
                for (Map.Entry<String, BigDecimal> dataEntry : yesterdayDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        yesterdayTotalFlow = yesterdayTotalFlow.add(flow);
                    }
                }
                // 今日、本月数据
                LinkedHashMap<String, BigDecimal> todayDataMap = totalFlowData.get(dateFormat.format(monthEnd.getTime()));
                for (Map.Entry<String, BigDecimal> dataEntry : todayDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        todayTotalFlow = todayTotalFlow.add(flow);
                        monthTotalFlow = monthTotalFlow.add(flow);
                    }
                }
            } else {// 非月初第一天
                // 本月数据
                for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : totalFlowData.entrySet()) {
                    LinkedHashMap<String, BigDecimal> value = entry.getValue();
                    for (Map.Entry<String, BigDecimal> dataEntry : value.entrySet()) {
                        BigDecimal flow = dataEntry.getValue();
                        if (flow != null) {
                            monthTotalFlow = monthTotalFlow.add(flow);
                        }
                    }
                }
                // 今日数据
                LinkedHashMap<String, BigDecimal> todayDataMap = totalFlowData.get(dateFormat.format(monthEnd.getTime()));
                for (Map.Entry<String, BigDecimal> dataEntry : todayDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        todayTotalFlow = todayTotalFlow.add(flow);
                    }
                }
                // 昨日数据
                LinkedHashMap<String, BigDecimal> yesterdayDataMap = totalFlowData.get(dateFormat.format(yesterdayStart.getTime()));
                for (Map.Entry<String, BigDecimal> dataEntry : yesterdayDataMap.entrySet()) {
                    BigDecimal flow = dataEntry.getValue();
                    if (flow != null) {
                        yesterdayTotalFlow = yesterdayTotalFlow.add(flow);
                    }
                }
            }

            vo.setMonthWaterSupply(monthTotalFlow);
            vo.setTodayWaterSupply(todayTotalFlow);
            vo.setYesterdayWaterSupply(yesterdayTotalFlow);
            vo.setLastTime(tsDateFormat.format(new Date(ts)));
        }

        // 取水数据
        stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr != null) {
            List<String> attributes = new ArrayList<>();
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    attributes = Collections.singletonList(deviceId + "." + deviceFullData.getProperty());
                }
            }
            if (attributes.size() > 0) {
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = obtainDataService.getDeviceData(attributes, monthStart.getTime(), monthEnd.getTime(), DateUtils.DAY, null, tenantId);
                BigDecimal todayWaterTake = null;
                for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : totalFlowData.entrySet()) {
                    LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                    if (dataMap != null) {
                        for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                            if (dataEntry.getValue() != null) {
                                if (todayWaterTake == null) {
                                    todayWaterTake = new BigDecimal("0");
                                }
                                todayWaterTake = todayWaterTake.add(dataEntry.getValue());
                            }
                        }
                    }
                }
                vo.setTodayWaterTake(todayWaterTake);
            }
        }

        return vo;
    }

    @Override
    public List<StationStatusVO> getList(String stationType, String projectId, String name, String status, TenantId tenantId) {
        List<StationStatusVO> resultList = new ArrayList<>();
        // 查询站点列表
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 99999, stationType, projectId);
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return resultList;
        }

        // 按站点名称筛选
        if (StringUtils.isNotBlank(name)) {
            stationList = stationList.stream().filter(station -> station.getName().contains(name)).collect(Collectors.toList());
        }

        // 查询站点的数据
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (StationEntity station : stationList) {
            // 查询站点的数据
            long ts = -1;
            String stationStatus = "offline";
            List<DeviceFullData> stationAttrDetailList = stationDataService.getStationDataDetail(station.getId(), "", true, tenantId);
            StationStatusVO vo = new StationStatusVO();
            vo.setStationId(station.getId());
            vo.setName(station.getName());
            if (stationAttrDetailList != null && !stationAttrDetailList.isEmpty()) {
                for (DeviceFullData stationAttr : stationAttrDetailList) {
                    if (stationAttr.getCollectionTime() > ts) {
                        ts = stationAttr.getCollectionTime();
                    }
                    if (stationAttr.isStatus()) {
                        stationStatus = "online";
                    }
                    if (stationAttr.getAlarmStatus() != null) {
                        stationStatus = "alarm";
                    }
                }

            }
            if (ts != -1) {
                vo.setLastTime(dateFormat.format(new Date(ts)));
            }
            vo.setStatus(stationStatus);
            vo.setLocation(station.getLocation());
            vo.setImgs(station.getImgs());

            resultList.add(vo);
        }

        // 按状态筛选
        if (StringUtils.isNotBlank(status)) {
            resultList = resultList.stream().filter(stationStatusVO -> stationStatusVO.getStatus().equals(status)).collect(Collectors.toList());
        }

        return resultList;
    }

    @Override
    public List<XiaofangshuanStatusVO> getListXiaofangshuan(String stationType, String projectId, String name, String status, TenantId tenantId) {
        List<XiaofangshuanStatusVO> resultList = new ArrayList<>();
        // 查询站点列表
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 99999, stationType, projectId);
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return resultList;
        }

        // 按站点名称筛选
        if (StringUtils.isNotBlank(name)) {
            stationList = stationList.stream().filter(station -> station.getName().contains(name)).collect(Collectors.toList());
        }

        // 查询站点的数据
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ExecutorService executorService = Executors.newCachedThreadPool();
        for (StationEntity station : stationList) {
            XiaofangshuanStatusVO vo = new XiaofangshuanStatusVO();
            vo.setStationId(station.getId());
            vo.setName(station.getName());
            vo.setAddress(station.getAddress());
            // 查询站点的数据
            long ts = -1;
            String stationStatus = "offline";
            List<DeviceFullData> stationAttrDetailList = stationDataService.getStationDataDetail(station.getId(), "", true, tenantId);

            if (stationAttrDetailList != null && !stationAttrDetailList.isEmpty()) {
                for (DeviceFullData stationAttr : stationAttrDetailList) {
                    if (stationAttr.getCollectionTime() > ts) {
                        ts = stationAttr.getCollectionTime();
                    }
                    if (stationAttr.isStatus()) {
                        stationStatus = "online";
                    }
                    if (stationAttr.getAlarmStatus() != null) {
                        stationStatus = "alarm";
                    }

                    if (stationAttr.getProperty().equals(DataConstants.DeviceAttrType.PRESSURE.getValue())) {
                        vo.setPressure(stationAttr.getValue());
                    }
                    if (stationAttr.getProperty().equals(DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue())) {
                        vo.setInstantaneousFlow(stationAttr.getValue());
                    }
                    if (stationAttr.getProperty().equals(DataConstants.DeviceAttrType.TOTAL_FLOW.getValue())) {
                        vo.setTotalFlow(stationAttr.getValue());
                    }
                }
            }
            if (ts != -1) {
                vo.setLastTime(dateFormat.format(new Date(ts)));
            }
            vo.setStatus(stationStatus);
            vo.setLocation(station.getLocation());
            vo.setImgs(station.getImgs());

            resultList.add(vo);
        }

        // 按状态筛选
        if (StringUtils.isNotBlank(status)) {
            resultList = resultList.stream().filter(stationStatusVO -> stationStatusVO.getStatus().equals(status)).collect(Collectors.toList());
        }

        return resultList;
    }

    @Override
    public JSONObject count(String type, String projectId, String name, String status, TenantId tenantId) {
        List<StationStatusVO> list = this.getList(type, projectId, name, status, tenantId);
        long online = list.stream().filter(a -> "online".equals(a.getStatus())).count();
        long offline = list.stream().filter(a -> "offline".equals(a.getStatus())).count();
        long alarm = list.stream().filter(a -> "alarm".equals(a.getStatus())).count();
        JSONObject result = new JSONObject();
        result.put("online", online);
        result.put("offline", offline);
        result.put("alarm", alarm);
        return result;
    }

    @Override
    public Object getWaterSupplyDataInfo(String type, TenantId tenantId) {
        // 查询站点列表
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 99999, type, null);
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return new ArrayList<>();
        }

        // 处理时间，今日时间和昨日时间
        Date now = new Date();
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);
        Date todayStartTime = instance.getTime();

        // 获取所有水厂站点的进、出水数据项
        Map<String, Map<String, List<String>>> stationAttributesMap = new LinkedHashMap<>();
        Map<String, BigDecimal> outInstantaneousFlowMap = new HashMap<>();
        Map<String, BigDecimal> outPressureMap = new HashMap<>();
        Map<String, BigDecimal> inPressureMap = new HashMap<>();
        Map<String, BigDecimal> outLevelMap = new HashMap<>();

        // 查询各个站点的进出水数据项以及最后出水瞬时流量、出口压力、出口液位
        for (StationEntity station : stationList) {

            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationInletAttr = null;
            StationAttrDTO stationOutletAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {// 进水数据
                    stationInletAttr = stationAttrDTO;
                }
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {// 出水数据
                    stationOutletAttr = stationAttrDTO;
                }
            }
            Map<String, List<String>> attributeMap = new HashMap<>();
            BigDecimal outInstantaneousFlow = null;
            BigDecimal outPressure = null;
            BigDecimal inPressure = null;
            BigDecimal outLevel = null;
            // 进水
            if (stationInletAttr != null) {
                List<String> attributes = new ArrayList<>();
                List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationInletAttr.getType(), true, tenantId);
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            attributes.add(deviceId + "." + deviceFullData.getProperty());
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                    if (DataConstants.DeviceAttrType.PRESSURE.getValue().equals(deviceFullData.getProperty()) && StringUtils.isNotBlank(deviceFullData.getValue())) {// 压力
                        try {
                            if (inPressure == null) {
                                inPressure = new BigDecimal(deviceFullData.getValue());
                            }
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                }
                attributeMap.put("inlet", attributes);
            }
            // 出水
            if (stationOutletAttr != null) {
                List<String> attributes = new ArrayList<>();
                List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationOutletAttr.getType(), true, tenantId);
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                        try {
                            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                            attributes.add(deviceId + "." + deviceFullData.getProperty());
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                    if (DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue().equals(deviceFullData.getProperty()) && StringUtils.isNotBlank(deviceFullData.getValue())) {// 瞬时流量
                        try {
                            if (outInstantaneousFlow == null) {
                                outInstantaneousFlow = new BigDecimal(deviceFullData.getValue());
                            } else {
                                outInstantaneousFlow = outInstantaneousFlow.add(new BigDecimal(deviceFullData.getValue()));
                            }
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
                    if (DataConstants.DeviceAttrType.PRESSURE.getValue().equals(deviceFullData.getProperty()) && StringUtils.isNotBlank(deviceFullData.getValue())) {// 压力
                        try {
                            if (outPressure == null) {
                                outPressure = new BigDecimal(deviceFullData.getValue());
                            }
                        } catch (Exception e) {
                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                        }
                    }
//                    if (DataConstants.DeviceAttrType.LEVEL.getValue().equals(deviceFullData.getProperty()) && StringUtils.isNotBlank(deviceFullData.getValue())) {// 液位
//                        try {
//                            if (outLevel == null) {
//                                outLevel = new BigDecimal(deviceFullData.getValue());
//                            }
//                        } catch (Exception e) {
//                            log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
//                        }
//                    }
                }
                attributeMap.put("outlet", attributes);
            }

            stationAttributesMap.put(station.getId(), attributeMap);
            outInstantaneousFlowMap.put(station.getId(), outInstantaneousFlow);
            outPressureMap.put(station.getId(), outPressure);
            inPressureMap.put(station.getId(), inPressure);
            outLevelMap.put(station.getId(), outLevel);
        }

        // 根据站点数据项查询今日进出水累计数据
        List<JSONObject> resultList = new ArrayList<>();
        for (StationEntity station : stationList) {
            JSONObject resultData = new JSONObject();
            resultData.put("id", station.getId());
            resultData.put("stationName", station.getName());
            resultData.put("location", station.getLocation());
            resultData.put("stationAdditionalInfo", station.getAdditionalInfo());
            resultData.put("stationObj", station);

            Map<String, List<String>> attributeMap = stationAttributesMap.get(station.getId());
            List<String> attrList = new ArrayList<>();
            for (Map.Entry<String, List<String>> entry : attributeMap.entrySet()) {
                List<String> value = entry.getValue();
                if (value != null && value.size() > 0) {
                    attrList.addAll(value);
                }
            }

            try {
                List<String> outletAttrs = attributeMap.get("outlet");
                List<String> inletAttrs = attributeMap.get("inlet");
                // 今日数据查询
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> todayDataMap =
                        obtainDataService.getDeviceData(attrList, todayStartTime.getTime(), now.getTime(), "day", null, tenantId);
                // 处理查询到的数据
                BigDecimal outletFlow = BigDecimal.ZERO;
                BigDecimal inletFlow = BigDecimal.ZERO;
                for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> mapEntry : todayDataMap.entrySet()) {
                    LinkedHashMap<String, BigDecimal> dataMap = mapEntry.getValue();
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        // attrKey
                        String key = dataEntry.getKey();
                        // 数据值
                        BigDecimal value = dataEntry.getValue();
                        if (value == null) {
                            continue;
                        }
                        // 判断是进口数据还是出口数据
                        if (outletAttrs.contains(key)) {// 出水口数据
                            outletFlow = outletFlow.add(value);
                        }
                        if (inletAttrs.contains(key)) {// 进水口数据
                            inletFlow = inletFlow.add(value);
                        }
                    }
                }

                resultData.put("outletWater", outletFlow);
                resultData.put("inletFlowWater", inletFlow);
            } catch (ThingsboardException e) {
                e.printStackTrace();
            }
            resultData.put("outInstantaneousFlow", outInstantaneousFlowMap.get(station.getId()));
            resultData.put("outPressure", outPressureMap.get(station.getId()));
            resultData.put("inPressure", inPressureMap.get(station.getId()));
            resultData.put("outLevel", outLevelMap.get(station.getId()));

            resultList.add(resultData);
        }

        return resultList;
    }

    @Override
    public Object productionAndSales(String stationId, String year, TenantId tenantId) throws ThingsboardException {
        // 查询指定年的供水量报表
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.YEAR, Integer.parseInt(year));
        instance.set(Calendar.MONTH, instance.getActualMinimum(Calendar.MONTH));
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMinimum(Calendar.DAY_OF_MONTH));
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date start = instance.getTime();

        instance.set(Calendar.MONTH, instance.getActualMaximum(Calendar.MONTH));
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);
        Date end = instance.getTime();

        List<String> attributes = new ArrayList<>();
        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);


        for (DeviceFullData deviceFullData : stationDataDetail) {
            if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    attributes.add(deviceId + "." + deviceFullData.getProperty());
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        // 查询数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, start.getTime(), end.getTime(), DateUtils.MONTH, null, tenantId);

        // 查询售水量报表
        IstarResponse response = operatingIncomeInputFeign.list(year, stationId);
        List<OperatingIncomeInput> inputs = JSON.parseArray(JSON.toJSONString(response.getData())).toJavaList(OperatingIncomeInput.class);

        List<JSONObject> resultList = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            OperatingIncomeInput data = inputs.get(i);
            BigDecimal waterSales = data.getWaterSales();
            String ts = data.getTs();

            LinkedHashMap<String, BigDecimal> supplyData = stationDataMap.get(ts);
            BigDecimal waterSupply = new BigDecimal("0");
            if (supplyData != null && supplyData.size() > 0) {
                for (Map.Entry<String, BigDecimal> entry : supplyData.entrySet()) {
                    BigDecimal value = entry.getValue();
                    if (value != null) {
                        waterSupply = waterSupply.add(value);
                    }
                }
            }

            JSONObject obj = new JSONObject();
            obj.put("ts", data.getTs());
            obj.put("waterSales", waterSales);
            obj.put("waterSupply", waterSupply);

            if (waterSales != null && waterSupply.doubleValue() != 0) {
                obj.put("rate", waterSupply.subtract(waterSales).divide(waterSupply, 4, BigDecimal.ROUND_DOWN).multiply(new BigDecimal("100")));
            } else {
                obj.put("rate", null);
            }

            resultList.add(obj);
        }


        return resultList;
    }

    @Override
    public Object getWaterSupplyDetailOriginalData(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 返回的列表数据预设
        Map<Integer, JSONObject> resultDataMap = new LinkedHashMap<>();
        // 报表类型
        String suffix = "";
        switch (queryType) {
            case "day":
                suffix = "时";
                queryType = "1h";
                for (int i = 0; i < 24; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            case "month":
                suffix = "日";
                queryType = "day";
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date(start));
                int days = instance.getActualMaximum(Calendar.DAY_OF_MONTH);
                for (int i = 1; i <= days; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            case "year":
                suffix = "月";
                queryType = "month";
                for (int i = 1; i <= 12; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);

        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 查询站点供水量数据
        Map<String, List<String>> attributesMap = new LinkedHashMap<>();
        String unit = "";
        for (StationEntity station : stationList) {
            List<String> attributes = new ArrayList<>();
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);


            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        attributes.add(deviceId + "." + deviceFullData.getProperty());
                        unit = deviceFullData.getUnit();
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
            attributesMap.put(station.getId(), attributes);
        }

        // 分组查询数据
        for (Map.Entry<String, List<String>> attrEntry : attributesMap.entrySet()) {
            String key = attrEntry.getKey();
            List<String> attrList = attrEntry.getValue();
            if (attrList == null || attrList.isEmpty()) {
                continue;
            }

            // 查询数据
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attrList, start, end, queryType, null, tenantId);


            // 设置站点供水量数据
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String timeKey = entry.getKey();
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                BigDecimal flow = new BigDecimal("0");
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    if (dataEntry.getValue() != null) {
                        flow = flow.add(dataEntry.getValue());
                    }
                }

                int resultMapKey = Integer.parseInt(StationDataUtil.shortenTimeKey(timeKey, queryType));
                JSONObject data = resultDataMap.get(resultMapKey);
                data.put(key, flow);

                resultDataMap.put(resultMapKey, data);
            }
        }

        // 设置固定行数据
        List<JSONObject> dataList = new ArrayList<>(resultDataMap.values());

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));

        // 动态结构
        for (StationEntity station : stationList) {
            DeviceDataTableInfoVO tableInfoVO = new DeviceDataTableInfoVO();
            tableInfoVO.setUnit(unit);
            tableInfoVO.setColumnName(station.getName());
            tableInfoVO.setColumnValue(station.getId());
            deviceDataTableInfoList.add(tableInfoVO);
        }

        DynamicTableVO result = new DynamicTableVO();

        result.setTableDataList(dataList);
        result.setTableInfo(deviceDataTableInfoList);

        return result;
    }

    @Override
    public Object getWaterSupplyPressureOriginalData(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 返回的列表数据预设
        Map<Integer, JSONObject> resultDataMap = new LinkedHashMap<>();
        // 报表类型
        String suffix = "";
        switch (queryType) {
            case "day":
                suffix = "时";
                queryType = "1h";
                for (int i = 0; i < 24; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            /*case "month":
                suffix = "日";
                queryType = "day";
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date(start));
                int days = instance.getActualMaximum(Calendar.DAY_OF_MONTH);
                for (int i = 1; i <= days; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            case "year":
                suffix = "月";
                queryType = "month";
                for (int i = 1; i <= 12; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;*/
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);

        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 查询站点供水量数据
        Map<String, List<String>> attributesMap = new LinkedHashMap<>();
        String unit = "";
        for (StationEntity station : stationList) {
            List<String> attributes = new ArrayList<>();
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);


            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.PRESSURE.getValue().equals(deviceFullData.getProperty())) {// 压力
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        attributes.add(deviceId + "." + deviceFullData.getProperty());
                        unit = deviceFullData.getUnit();
                        break;
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
            attributesMap.put(station.getId(), attributes);
        }

        // 分组查询数据
        for (Map.Entry<String, List<String>> attrEntry : attributesMap.entrySet()) {
            String key = attrEntry.getKey();
            List<String> attrList = attrEntry.getValue();
            if (attrList == null || attrList.isEmpty()) {
                continue;
            }

            // 查询数据
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attrList, start, end, queryType, null, tenantId);


            // 设置站点数据
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String timeKey = entry.getKey();
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                BigDecimal flow = new BigDecimal("0");
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    if (dataEntry.getValue() != null) {
                        flow = dataEntry.getValue();
                    }
                }

                int resultMapKey = Integer.parseInt(StationDataUtil.shortenTimeKey(timeKey, queryType));
                JSONObject data = resultDataMap.get(resultMapKey);
                data.put(key, flow);

                resultDataMap.put(resultMapKey, data);
            }
        }

        // 设置固定行数据
        List<JSONObject> dataList = new ArrayList<>(resultDataMap.values());

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));

        // 动态结构
        for (StationEntity station : stationList) {
            DeviceDataTableInfoVO tableInfoVO = new DeviceDataTableInfoVO();
            tableInfoVO.setUnit(unit);
            tableInfoVO.setColumnName(station.getName());
            tableInfoVO.setColumnValue(station.getId());
            deviceDataTableInfoList.add(tableInfoVO);
        }

        DynamicTableVO result = new DynamicTableVO();

        result.setTableDataList(dataList);
        result.setTableInfo(deviceDataTableInfoList);

        return result;
    }

    @Override
    public Object waterPlantMonthTotal(String stationType, List<String> stationIdList, TenantId tenantId) throws ThingsboardException {
        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);

        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 查询站点供水量数据
        Map<String, List<String>> attributesMap = new LinkedHashMap<>();
        String unit = "";
        for (StationEntity station : stationList) {
            List<String> attributes = new ArrayList<>();
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);


            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        attributes.add(deviceId + "." + deviceFullData.getProperty());
                        unit = deviceFullData.getUnit();
                        break;
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
            attributesMap.put(station.getId(), attributes);
        }
        Date date = new Date();
        String s = DateUtils.date2Str(date, "yyyy-MM");
        Date start = DateUtils.str2Date(s, "yyyy-MM");
        // 分组查询数据
        Map<String, BigDecimal> flowDataMap = new HashMap<>();
        for (Map.Entry<String, List<String>> attrEntry : attributesMap.entrySet()) {
            String key = attrEntry.getKey();
            List<String> attrList = attrEntry.getValue();
            if (attrList == null || attrList.isEmpty()) {
                continue;
            }

            // 查询数据
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attrList, start.getTime(), date.getTime(), "1h", null, tenantId);

            // 设置站点数据
            BigDecimal flow = new BigDecimal("0");
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    if (dataEntry.getValue() != null) {
                        flow = flow.add(dataEntry.getValue());
                    }
                }

            }
            flowDataMap.put(key, flow);
        }
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : flowDataMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = entry.getValue();
            JSONObject data = new JSONObject();
            data.put("stationId", key);
            StationEntity station = stationMap.get(key);
            if (station != null) {
                data.put("stationName", station.getName());
            }
            data.put("value", value);

            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public Object getStationDataByAttr(String stationId, String attr, String queryType, Long startTime, Long endTime, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点数据以及站点的动态属性列表
        /*List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }*/
        List<StationAttrEntity> attrList = stationFeignClient.getStationAllAttrList(station.getId());
        List<String> attributes = new ArrayList<>();
        if (attrList != null) {
            attributes = attrList.stream()
                    .filter(stationAttrEntity -> attr.equals(stationAttrEntity.getAttr()))
                    .map(stationAttrEntity -> UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId())) + "." + stationAttrEntity.getAttr())
                    .collect(Collectors.toList());
        }


        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attributes, startTime, endTime, queryType, null, tenantId);

        // 数据整理
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            JSONObject data = new JSONObject();
            String suffix = key.substring(key.length() - 2);
            data.put("ts", suffix);

            // 统计数据
            BigDecimal value = null;
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                if (dataEntry.getValue() != null) {
                    value = dataEntry.getValue();
                }
            }
            data.put("value", value);

            resultList.add(data);
        }
        return resultList;
    }

    @Override
    public DynamicTableVO getPointMonitor(String stationType, List<String> stationIdList, String attr, String queryType, String time, TenantId tenantId) throws Exception {
        String exeQueryType = "";
        switch (queryType) {
            case "day":
                exeQueryType = "1h";
                break;
            case "month":
                exeQueryType = "day";
                break;
            case "year":
                exeQueryType = "month";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅允许day、month、year", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);
        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 属性对应的站点列表
        Map<String, String> attrStationMap = new HashMap<>();
        // 获取到每个站点的指定attr
        String attrName = "";
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());
            for (StationAttrEntity stationAttrEntity : stationAttrList) {
                if (attr.equals(stationAttrEntity.getAttr())) {
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                        String attrKey = deviceId + "." + stationAttrEntity.getAttr();
                        attrName = stationAttrEntity.getName();
                        attrStationMap.put(attrKey, station.getId());
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }

        }

        // 数据查询
        Map<String, Date> timeRange = StationDataUtil.getTimeRange(time, queryType);
        Date start = timeRange.get("start");
        Date end = timeRange.get("end");
        List<String> allAttributeList = new ArrayList<>(attrStationMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(allAttributeList, start.getTime(), end.getTime(), exeQueryType, null, tenantId);

        // 数据分组
        List<JSONObject> dataList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

            // 数据
            JSONObject data = new JSONObject();
            data.put("ts", timeKey);
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                String dataKey = dataEntry.getKey();
                BigDecimal value = dataEntry.getValue();
                if (value == null) {
                    continue;
                }
                String stationId = attrStationMap.get(dataKey);

                BigDecimal dataValue = data.getBigDecimal(stationId + "--" + attr);
                if (dataValue == null) {
                    dataValue = new BigDecimal("0");
                }
                dataValue = dataValue.add(value);
                data.put(stationId + "--" + attr, dataValue);
            }
            dataList.add(data);
        }

        // 统计数据
        Map<String, JSONObject> countMap = new LinkedHashMap<>();
        countMap.put("max", new JSONObject());
        countMap.put("maxTs", new JSONObject());
        countMap.put("min", new JSONObject());
        countMap.put("minTs", new JSONObject());
        countMap.put("avg", new JSONObject());
        countMap.put("total", new JSONObject());

        for (StationEntity station : stationList) {
            String stationId = station.getId();
            // 统计数据
            BigDecimal valueMax = null;
            BigDecimal valueMin = null;
            String valueMaxTs = null;
            String valueMinTs = null;
            BigDecimal valueTotal = new BigDecimal("0");
            BigDecimal valueAvg = new BigDecimal("0");
            int valueCount = 0;
            for (JSONObject data : dataList) {
                BigDecimal dataValue = data.getBigDecimal(stationId + "--" + attr);
                if (dataValue != null) {
                    if (valueMax == null || valueMax.doubleValue() < dataValue.doubleValue()) {
                        valueMax = dataValue;
                        valueMaxTs = data.getString("ts");
                    }
                    if (valueMin == null || valueMin.doubleValue() > dataValue.doubleValue()) {
                        valueMin = dataValue;
                        valueMinTs = data.getString("ts");
                    }
                    valueTotal = valueTotal.add(dataValue);
                    valueCount++;
                }
            }
            // 计算平均值
            if (valueCount != 0) {
                valueAvg = valueTotal.divide(BigDecimal.valueOf(valueCount), 2, BigDecimal.ROUND_DOWN);
            }

            JSONObject maxObj = countMap.get("max");
            maxObj.put(stationId + "--" + attr, valueMax);
            countMap.put("max", maxObj);

            JSONObject maxTsObj = countMap.get("maxTs");
            maxTsObj.put(stationId + "--" + attr, valueMaxTs);
            countMap.put("maxTs", maxTsObj);

            JSONObject minObj = countMap.get("min");
            minObj.put(stationId + "--" + attr, valueMin);
            countMap.put("min", minObj);

            JSONObject minTsObj = countMap.get("minTs");
            minTsObj.put(stationId + "--" + attr, valueMinTs);
            countMap.put("minTs", minTsObj);

            JSONObject avgObj = countMap.get("avg");
            avgObj.put(stationId + "--" + attr, valueAvg);
            countMap.put("avg", avgObj);

            JSONObject totalObj = countMap.get("total");
            totalObj.put(stationId + "--" + attr, valueTotal);
            countMap.put("total", totalObj);
        }

        for (Map.Entry<String, JSONObject> countObj : countMap.entrySet()) {
            String key = countObj.getKey();
            JSONObject data = countObj.getValue();
            String ts = "";
            switch (key) {
                case "max":
                    ts = "最大值";
                    break;
                case "maxTs":
                    ts = "最大值时间";
                    break;
                case "min":
                    ts = "最小值";
                    break;
                case "minTs":
                    ts = "最小值时间";
                    break;
                case "avg":
                    ts = "平均值";
                    break;
                case "total":
                    ts = "合计";
                    break;
            }
            data.put("ts", ts);
            dataList.add(data);
        }


        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (StationEntity station : stationList) {
            String stationId = station.getId();
            DeviceDataTableInfoVO outletVO = new DeviceDataTableInfoVO();
            outletVO.setColumnName(station.getName() + "--" + attrName);
            outletVO.setColumnValue(stationId + "--" + attr);
            outletVO.setUnit("m³");
            deviceDataTableInfoList.add(outletVO);

        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public DynamicTableVO getWaterProcessDetailReport(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 返回的列表数据预设
        Map<Integer, JSONObject> resultDataMap = new LinkedHashMap<>();
        // 报表类型
        String suffix = "";
        switch (queryType) {
            case "day":
                suffix = "时";
                queryType = "1h";
                for (int i = 0; i < 24; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            case "month":
                suffix = "日";
                queryType = "day";
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date(start));
                int days = instance.getActualMaximum(Calendar.DAY_OF_MONTH);
                for (int i = 1; i <= days; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            case "year":
                suffix = "月";
                queryType = "month";
                for (int i = 1; i <= 12; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i, obj);
                }
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);

        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 查询站点供水量数据
        Map<String, List<String>> attributesMap = new LinkedHashMap<>();
        String unit = "";
        for (StationEntity station : stationList) {
            List<String> attributes = new ArrayList<>();
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                throw new ThingsboardException("水厂未设置供水相关的动态属性分组", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);


            for (DeviceFullData deviceFullData : stationDataDetail) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(deviceFullData.getProperty())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        attributes.add(deviceId + "." + deviceFullData.getProperty());
                        unit = deviceFullData.getUnit();
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
            attributesMap.put(station.getId(), attributes);
        }


        // 固定行
        JSONObject maxObj = new JSONObject();
        maxObj.put("ts", "最大值");
        JSONObject maxTimeObj = new JSONObject();
        maxTimeObj.put("ts", "最大值时间");
        JSONObject minObj = new JSONObject();
        minObj.put("ts", "最小值");
        JSONObject minTimeObj = new JSONObject();
        minTimeObj.put("ts", "最小值时间");
        JSONObject avgObj = new JSONObject();
        avgObj.put("ts", "平均值");
        JSONObject totalObj = new JSONObject();
        totalObj.put("ts", "合计");

        // 分组查询数据
        for (Map.Entry<String, List<String>> attrEntry : attributesMap.entrySet()) {
            String key = attrEntry.getKey();
            List<String> attrList = attrEntry.getValue();
            if (attrList == null || attrList.isEmpty()) {
                continue;
            }

            // 查询数据
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attrList, start, end, queryType, null, tenantId);

            BigDecimal total = new BigDecimal("0");
            BigDecimal max = null;
            BigDecimal min = null;
            String maxFlag = "-";// 记录最大值的时间
            String minFlag = "-";// 记录最小值的时间
            int num = 0; // 次数

            // 合计Map
            Map<Integer, BigDecimal> totalMap = new LinkedHashMap<>();

            // 设置站点供水量数据
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String timeKey = entry.getKey();
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                BigDecimal flow = new BigDecimal("0");
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    if (dataEntry.getValue() != null) {
                        flow = flow.add(dataEntry.getValue());
                    }
                }

                total = total.add(flow);
                num++;
                if (max == null || max.doubleValue() < flow.doubleValue()) {
                    max = flow;
                    maxFlag = StationDataUtil.shortenTimeKey(timeKey, queryType);
                }
                if (min == null || min.doubleValue() > flow.doubleValue()) {
                    min = flow;
                    minFlag = StationDataUtil.shortenTimeKey(timeKey, queryType);
                }

                int resultMapKey = Integer.parseInt(StationDataUtil.shortenTimeKey(timeKey, queryType));
                JSONObject data = resultDataMap.get(resultMapKey);
                data.put(key, flow);

                resultDataMap.put(resultMapKey, data);

                // 设置合计map
                BigDecimal nowTotal = new BigDecimal("0");
                if (totalMap.containsKey(resultMapKey)) {
                    nowTotal = totalMap.get(resultMapKey);
                }
                nowTotal = nowTotal.add(flow);
                totalMap.put(resultMapKey, nowTotal);
            }

            // 设置最大最小值平均值等数据
            if (max != null) {
                maxObj.put(key, max);
                maxTimeObj.put(key, Integer.parseInt(maxFlag) + suffix);
            }
            if (min != null) {
                minObj.put(key, min);
                minTimeObj.put(key, minFlag + suffix);
            }
            // 平均值
            if (num != 0) {
                avgObj.put(key, total.divide(BigDecimal.valueOf(num), 2, BigDecimal.ROUND_DOWN));
            }
            // 合计
            totalObj.put(key, total);
        }

        // 设置固定行数据
        List<JSONObject> dataList = new ArrayList<>(resultDataMap.values());
        dataList.add(maxObj);
        dataList.add(maxTimeObj);
        dataList.add(minObj);
        dataList.add(minTimeObj);
        dataList.add(avgObj);
        dataList.add(totalObj);

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间", "total:合计:" + unit));

        // 动态结构
        for (StationEntity station : stationList) {
            DeviceDataTableInfoVO tableInfoVO = new DeviceDataTableInfoVO();
            tableInfoVO.setUnit(unit);
            tableInfoVO.setColumnName(station.getName());
            tableInfoVO.setColumnValue(station.getId());
            deviceDataTableInfoList.add(tableInfoVO);
        }

        DynamicTableVO result = new DynamicTableVO();
        // 计算合计
        for (JSONObject data : dataList) {
            BigDecimal total = new BigDecimal("0");
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                if (!"ts".equals(key) && !"total".equals(key)) {
                    if (entry.getValue() instanceof BigDecimal) {
                        total = total.add((BigDecimal) entry.getValue());
                    }
                }
            }
            data.put("total", total);
        }

        result.setTableDataList(dataList);
        result.setTableInfo(deviceDataTableInfoList);

        return result;
    }

    @Override
    @Cacheable(cacheNames = "getWaterSupplyTotal", key = "{#time,#type}")
    public StationWaterSupplyVO getWaterSupplyTotal(String time, String type, TenantId tenantId) {
        List<StationWaterSupplyVO> waterSupplyInfo = this.getWaterSupplyInfo("", type, "", tenantId);
        StationWaterSupplyVO stationWaterSupplyVO = new StationWaterSupplyVO();
        stationWaterSupplyVO.setMonthWaterSupply(BigDecimal.ZERO);
        stationWaterSupplyVO.setTodayWaterSupply(BigDecimal.ZERO);
        stationWaterSupplyVO.setYesterdayWaterSupply(BigDecimal.ZERO);
        stationWaterSupplyVO.setName("梓莲达");
        for (StationWaterSupplyVO waterSupplyVO : waterSupplyInfo) {
            stationWaterSupplyVO.setTodayWaterSupply(stationWaterSupplyVO.getTodayWaterSupply().add(waterSupplyVO.getTodayWaterSupply() == null ? BigDecimal.ZERO : waterSupplyVO.getTodayWaterSupply()));
            stationWaterSupplyVO.setYesterdayWaterSupply(stationWaterSupplyVO.getYesterdayWaterSupply().add(waterSupplyVO.getYesterdayWaterSupply() == null ? BigDecimal.ZERO : waterSupplyVO.getYesterdayWaterSupply()));
            stationWaterSupplyVO.setMonthWaterSupply(stationWaterSupplyVO.getMonthWaterSupply().add(waterSupplyVO.getMonthWaterSupply() == null ? BigDecimal.ZERO : waterSupplyVO.getMonthWaterSupply()));
        }
        return stationWaterSupplyVO;
    }

    @Override
    @Cacheable(cacheNames = "getWaterSupplyTotalAll", key = "{#time,#name}")
    public BigDecimal getWaterSupplyTotalAll(String time, String name, TenantId tenantId) {
        // 西部，北部，金峰
        String deviceId = "1ee20a103973a1091506ddf731ece97,1eeba646cf18e909f077f88cf1110e4,1eeba64a3a660f09f077f88cf1110e4";
        if (name.contains("西部")) {
            deviceId = "1ee20a103973a1091506ddf731ece97";
        } else if (name.contains("北部")) {
            deviceId = "1eeba646cf18e909f077f88cf1110e4";
        } else if (name.contains("金峰")) {
            deviceId = "1eeba64a3a660f09f077f88cf1110e4";
        }
        String[] split = deviceId.split(",");
        BigDecimal result = BigDecimal.ZERO;
        for (String id : split) {
            List<DeviceFullData> list = deviceService.getDeviceFullData(tenantId, new DeviceId(UUIDConverter.fromString(id)), "");
            for (DeviceFullData s : list) {
                if (s.getProperty().equals(DataConstants.DeviceAttrType.TOTAL_FLOW.getValue())) {
                    try {
                        result = result.add(BigDecimal.valueOf(Double.valueOf(s.getValue())));
                    } catch (Exception e) {
                    }
                }
            }
        }
        return result;
    }

    /**
     * 查询指定数据项的数据和按照指定方式查询其同比/环比/定基比的数据
     * 并且统计出最大值最小值平均值等数据
     *
     * @param attributes   要查询的数据项
     * @param queryType    报表类型。日、月、年
     * @param time         时间
     * @param compareType  1：同比；2：环比；3：定基比（仅日报表类型有）
     * @param unit         单位
     * @param exeQueryType 执行的查询类型
     * @param tenantId     租户ID
     * @return 数据
     */
    public Object getWaterSupplyDataReport(List<String> attributes, String queryType, String time, String compareType, String unit, String exeQueryType, TenantId tenantId) throws Exception {
        // 上期开始时间
        Date lastStartTime = null;
        Date lastEndTime = null;

        Map<String, Date> timeRange = StationDataUtil.getTimeRange(time, queryType);
        // 本期开始时间
        Date startTime = timeRange.get("start");
        // 本期结束时间
        Date endTime = timeRange.get("end");

        // 处理时间
        Map<String, Date> lastTimeRange = null;
        SimpleDateFormat dataProcessDateFormat = null;
        SimpleDateFormat timeProcessDateFormat = null;
        Calendar instance = Calendar.getInstance();

        String timeSuffix = "";
        switch (queryType) {
            case "day":
                if (StringUtils.isBlank(exeQueryType)) {
                    exeQueryType = "1h";
                }

                // 获取要对比的时间区间
                instance.setTime(startTime);
                if ("1".equals(compareType) || "3".equals(compareType)) {// 同比、定基比
                    instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
                }
                if ("2".equals(compareType)) {// 环比
                    instance.set(Calendar.DAY_OF_MONTH, instance.get(Calendar.DAY_OF_MONTH) - 1);
                }
                lastTimeRange = StationDataUtil.getTimeRange(instance.getTime(), queryType);

                dataProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd HH");
                timeProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                if (StringUtils.isBlank(exeQueryType)) {
                    timeSuffix = "时";
                }
                break;
            case "month":
                if (StringUtils.isBlank(exeQueryType)) {
                    exeQueryType = "day";
                }

                // 获取要对比的时间区间
                instance.setTime(startTime);
                if ("1".equals(compareType)) {// 同比
                    instance.set(Calendar.YEAR, instance.get(Calendar.YEAR) - 1);
                }
                if ("2".equals(compareType)) {// 环比
                    instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
                }
                lastTimeRange = StationDataUtil.getTimeRange(instance.getTime(), queryType);

                dataProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                timeProcessDateFormat = new SimpleDateFormat("yyyy-MM");
                timeSuffix = "日";
                break;
            case "year":
                if (StringUtils.isBlank(exeQueryType)) {
                    exeQueryType = "month";
                }

                // 获取要对比的时间区间
                instance.setTime(startTime);
                if ("2".equals(compareType)) {// 环比
                    instance.set(Calendar.YEAR, instance.get(Calendar.YEAR) - 1);
                }
                lastTimeRange = StationDataUtil.getTimeRange(instance.getTime(), queryType);

                dataProcessDateFormat = new SimpleDateFormat("yyyy-MM");
                timeProcessDateFormat = new SimpleDateFormat("yyyy");
                timeSuffix = "月";
                break;
            default:
                throw new ThingsboardException("非法的分析类型, 仅支持day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        lastStartTime = lastTimeRange.get("start");
        lastEndTime = lastTimeRange.get("end");

        // 查询本期
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, startTime.getTime(), endTime.getTime(), exeQueryType, null, tenantId);
        // 查询上期
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> lastStationDataMap =
                obtainDataService.getDeviceData(attributes, lastStartTime.getTime(), lastEndTime.getTime(), exeQueryType, null, tenantId);

        // 数据整理
        Map<String, JSONObject> resultDataMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            JSONObject data = new JSONObject();
            String suffix = key.substring(key.length() - 2);
            data.put("ts", suffix);

            // 统计数据
            BigDecimal value = null;
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                if (dataEntry.getValue() != null) {
                    if (value == null) {
                        value = new BigDecimal("0");
                    }
                    value = value.add(dataEntry.getValue());
                }
            }

            data.put(timeProcessDateFormat.format(startTime), value);
            resultDataMap.put(suffix, data);
        }

        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : lastStationDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            String suffix = key.substring(key.length() - 2);
            JSONObject data = resultDataMap.get(suffix);
            if (data == null) {
                data = new JSONObject();
            }
            data.put("ts", suffix);

            // 统计数据
            BigDecimal value = null;
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                if (dataEntry.getValue() != null) {
                    if (value == null) {
                        value = new BigDecimal("0");
                    }
                    value = value.add(dataEntry.getValue());
                }
            }

            data.put(timeProcessDateFormat.format(lastStartTime), value);
            resultDataMap.put(suffix, data);
        }

        // 数据统计。计算变化系数、差值率。统计最大最小值、计算平均值
        String timeKey = timeProcessDateFormat.format(startTime);
        String lastTimeKey = timeProcessDateFormat.format(lastStartTime);
        List<JSONObject> dataList = new ArrayList<>(resultDataMap.values());

        // 本期统计数据
        String timeKeyMaxTs = "";
        BigDecimal timeKeyMax = null;
        String timeKeyMinTs = "";
        BigDecimal timeKeyMin = null;
        BigDecimal timeKeyAvg = null;
        BigDecimal timeKeyTotal = new BigDecimal("0");
        int timeKeyCount = 0;

        // 上期
        String lastTimeKeyMaxTs = "";
        BigDecimal lastTimeKeyMax = null;
        String lastTimeKeyMinTs = "";
        BigDecimal lastTimeKeyMin = null;
        BigDecimal lastTimeKeyAvg = null;
        BigDecimal lastTimeKeyTotal = new BigDecimal("0");
        int lastTimeKeyCount = 0;
        for (int i = 0; i < dataList.size(); i++) {
            JSONObject data = dataList.get(i);
            BigDecimal timeKeyData = data.getBigDecimal(timeKey);
            BigDecimal lastTimeKeyData = data.getBigDecimal(lastTimeKey);

            // 计算的对比数据
            if (timeKeyData != null && lastTimeKeyData != null) {
                // 计算差值率 （本期-上期）/ 上期
                BigDecimal differenceRate = timeKeyData.subtract(lastTimeKeyData).divide(lastTimeKeyData, 2, BigDecimal.ROUND_UP);

                // 计算变化率。（本期+上期）/ 2 / 本期
                BigDecimal changeRate = timeKeyData.add(lastTimeKeyData)
                        .divide(new BigDecimal("2"), 2, BigDecimal.ROUND_UP)
                        .divide(timeKeyData, 2, BigDecimal.ROUND_UP);

                data.put("differenceRate", differenceRate);
                data.put("changeRate", changeRate);
            }

            if (timeKeyData != null) {
                if (timeKeyMax == null || timeKeyData.doubleValue() > timeKeyMax.doubleValue()) {
                    timeKeyMax = timeKeyData;
                    timeKeyMaxTs = data.getString("ts") + timeSuffix;
                }

                if (timeKeyMin == null || timeKeyData.doubleValue() < timeKeyMin.doubleValue()) {
                    timeKeyMin = timeKeyData;
                    timeKeyMinTs = data.getString("ts") + timeSuffix;
                }
                timeKeyCount++;
                timeKeyTotal = timeKeyTotal.add(timeKeyData);
            }

            if (lastTimeKeyData != null) {
                if (lastTimeKeyMax == null || lastTimeKeyData.doubleValue() > lastTimeKeyMax.doubleValue()) {
                    lastTimeKeyMax = timeKeyData;
                    lastTimeKeyMaxTs = data.getString("ts") + timeSuffix;
                }

                if (lastTimeKeyMin == null || lastTimeKeyData.doubleValue() < lastTimeKeyMin.doubleValue()) {
                    lastTimeKeyMin = lastTimeKeyData;
                    lastTimeKeyMinTs = data.getString("ts") + timeSuffix;
                }
                lastTimeKeyCount++;
                lastTimeKeyTotal = lastTimeKeyTotal.add(lastTimeKeyData);
            }
            data.put("ts", data.getString("ts") + timeSuffix);
        }

        // 统计结果汇总
        List<JSONObject> countInfoList = new ArrayList<>();
        JSONObject timeKeyCountInfo = new JSONObject();
        timeKeyCountInfo.put("ts", timeKey + "(" + unit + ")");
        timeKeyCountInfo.put("max", timeKeyMax);
        timeKeyCountInfo.put("maxTs", timeKeyMaxTs);
        timeKeyCountInfo.put("min", timeKeyMin);
        timeKeyCountInfo.put("minTs", timeKeyMinTs);
        if (timeKeyCount != 0) {
            timeKeyAvg = timeKeyTotal.divide(BigDecimal.valueOf(timeKeyCount), 2, BigDecimal.ROUND_DOWN);
        }
        timeKeyCountInfo.put("avg", timeKeyAvg);

        JSONObject lastTimeKeyCountInfo = new JSONObject();
        lastTimeKeyCountInfo.put("ts", lastTimeKey + "(" + unit + ")");
        lastTimeKeyCountInfo.put("max", lastTimeKeyMax);
        lastTimeKeyCountInfo.put("maxTs", lastTimeKeyMaxTs);
        lastTimeKeyCountInfo.put("min", lastTimeKeyMin);
        lastTimeKeyCountInfo.put("minTs", lastTimeKeyMinTs);
        if (lastTimeKeyCount != 0) {
            lastTimeKeyAvg = lastTimeKeyTotal.divide(BigDecimal.valueOf(lastTimeKeyCount), 2, BigDecimal.ROUND_DOWN);
        }
        lastTimeKeyCountInfo.put("avg", lastTimeKeyAvg);
        countInfoList.add(timeKeyCountInfo);
        countInfoList.add(lastTimeKeyCountInfo);


        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();

        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList(
                "ts:时间",
                timeKey + ":" + timeKey + ":" + unit,
                lastTimeKey + ":" + lastTimeKey + ":" + unit,
                "differenceRate:差值率:%",
                "changeRate:变化系数"
        ));

        DynamicTableVO dynamicTableVO = new DynamicTableVO();
        dynamicTableVO.setTableDataList(dataList);
        dynamicTableVO.setTableInfo(deviceDataTableInfoList);

        JSONObject result = new JSONObject();
        result.put("baseTable", dynamicTableVO);
        result.put("countTable", countInfoList);

        return result;
    }
}
