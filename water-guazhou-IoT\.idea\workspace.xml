<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1047516e-fa6a-4e69-b28a-cd54c6ad8784" name="Changes" comment="补全缺失代码，修改配置文件">
      <change beforePath="$PROJECT_DIR$/application/.gradle/2.13/taskArtifacts/cache.properties.lock" beforeDir="false" afterPath="$PROJECT_DIR$/application/.gradle/2.13/taskArtifacts/cache.properties.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/application/.gradle/2.13/taskArtifacts/fileHashes.bin" beforeDir="false" afterPath="$PROJECT_DIR$/application/.gradle/2.13/taskArtifacts/fileHashes.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/application/.gradle/2.13/taskArtifacts/fileSnapshots.bin" beforeDir="false" afterPath="$PROJECT_DIR$/application/.gradle/2.13/taskArtifacts/fileSnapshots.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/application/.gradle/2.13/taskArtifacts/taskArtifacts.bin" beforeDir="false" afterPath="$PROJECT_DIR$/application/.gradle/2.13/taskArtifacts/taskArtifacts.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/application/src/main/resources/thingsboard.yml" beforeDir="false" afterPath="$PROJECT_DIR$/application/src/main/resources/thingsboard.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.6.3\.m2" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2v7p9YkK5whaD0hXeAdSosaZRN0" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.ThingsboardServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Maven.thingsboard [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.thingsboard [install].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;
  }
}</component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="ThingsboardServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.thingsboard.server.ThingsboardServerApplication" />
      <module name="application" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.thingsboard.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="water-guazhou-IoT" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ThingsboardServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1047516e-fa6a-4e69-b28a-cd54c6ad8784" name="Changes" comment="" />
      <created>1743505817014</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743505817014</updated>
    </task>
    <task id="LOCAL-00001" summary="补全代码">
      <option name="closed" value="true" />
      <created>1744266988354</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1744266988354</updated>
    </task>
    <task id="LOCAL-00002" summary="补充gitignore文件">
      <option name="closed" value="true" />
      <created>1744267094015</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1744267094015</updated>
    </task>
    <task id="LOCAL-00003" summary="补全缺失代码，修改配置文件">
      <option name="closed" value="true" />
      <created>1745462916380</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1745462916380</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="补全代码" />
    <MESSAGE value="补充gitignore文件" />
    <MESSAGE value="补全缺失代码，修改配置文件" />
    <option name="LAST_COMMIT_MESSAGE" value="补全缺失代码，修改配置文件" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/StationDataController.java</url>
          <line>137</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>