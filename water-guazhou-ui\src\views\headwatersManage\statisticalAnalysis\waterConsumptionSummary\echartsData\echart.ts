// 水源地能耗报告柱状图配置
export function barOption() {
  const option = {
    title: {
      text: '',
      textStyle: {
        color: '#5470C6',
        fontSize: '14px'
      },
      top: 10
    },
    grid: {
      left: 90,
      right: 90,
      top: 70,
      bottom: 80 // 增加底部空间给X轴标签
    },
    legend: {
      top: 20,
      type: 'scroll',
      width: '500',
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        let result = params[0].name + '<br/>'
        params.forEach((param: any) => {
          result += param.marker + param.seriesName + ': ' + param.value + '<br/>'
        })
        return result
      }
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        rotate: 30,
        interval: 0, // 显示所有标签
        fontSize: 12,
        margin: 10
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '数值',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: []
  }
  return option
}
