<!-- 水源地取水总量、耗电总量和吨水电耗 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'水源地取水总量、耗电总量和吨水电耗':'水源地能耗图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:bar-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        class="chart-container"
      >
        <!-- 图表模式 - 左右布局 -->
        <div class="chart-left">
          <!-- 左侧饼图区域 -->
          <div class="pie-charts">
            <div class="pie-chart-item">
              <h4 class="chart-title">吨水电耗分布</h4>
              <VChart
                ref="refPieChart1"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.pieChart1Option"
                class="pie-chart"
              ></VChart>
            </div>
            <div class="pie-chart-item">
              <h4 class="chart-title">取水量分布</h4>
              <VChart
                ref="refPieChart2"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.pieChart2Option"
                class="pie-chart"
              ></VChart>
            </div>
            <div class="pie-chart-item">
              <h4 class="chart-title">用电量分布</h4>
              <VChart
                ref="refPieChart3"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.pieChart3Option"
                class="pie-chart"
              ></VChart>
            </div>
          </div>
        </div>
        <div class="chart-right">
          <!-- 右侧混合图表 -->
          <div class="main-chart">
            <h4 class="chart-title">综合对比图表</h4>
            <VChart
              ref="refChart"
              :theme="useAppStore().isDark?'dark':'light'"
              :option="state.chartOption"
              class="mixed-chart"
            ></VChart>
          </div>
        </div>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'" class="list-container">
        <!-- 列表模式的饼图区域 -->
        <div class="list-pie-charts">
          <div class="list-pie-item">
            <h4 class="chart-title">吨水电耗分布</h4>
            <VChart
              ref="refListPieChart1"
              :theme="useAppStore().isDark?'dark':'light'"
              :option="state.listPieChart1Option"
              class="list-pie-chart"
            ></VChart>
          </div>
          <div class="list-pie-item">
            <h4 class="chart-title">取水量分布</h4>
            <VChart
              ref="refListPieChart2"
              :theme="useAppStore().isDark?'dark':'light'"
              :option="state.listPieChart2Option"
              class="list-pie-chart"
            ></VChart>
          </div>
          <div class="list-pie-item">
            <h4 class="chart-title">用电量分布</h4>
            <VChart
              ref="refListPieChart3"
              :theme="useAppStore().isDark?'dark':'light'"
              :option="state.listPieChart3Option"
              class="list-pie-chart"
            ></VChart>
          </div>
        </div>

        <!-- 数据表格 -->
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyAndEnergyData, getWaterSupplyDetailReport } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { barOption } from './echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()
const erd = elementResizeDetectorMaker()
const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any,
  title: string,
  activeName: string,
  chartOption: any,
  pieChart1Option: any,
  pieChart2Option: any,
  pieChart3Option: any,
  listPieChart1Option: any,
  listPieChart2Option: any,
  listPieChart3Option: any,
  dataList: any
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  pieChart1Option: null,
  pieChart2Option: null,
  pieChart3Option: null,
  listPieChart1Option: null,
  listPieChart2Option: null,
  listPieChart3Option: null,
  dataList: {}
})

const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()
const refPieChart1 = ref<IECharts>()
const refPieChart2 = ref<IECharts>()
const refPieChart3 = ref<IECharts>()
const refListPieChart1 = ref<IECharts>()
const refListPieChart2 = ref<IECharts>()
const refListPieChart3 = ref<IECharts>()

watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      setTimeout(() => {
        refuseChart()
      }, 100) // 延迟确保DOM渲染完成
    })
  } else if (state.activeName === 'list') {
    nextTick(() => {
      setTimeout(() => {
        refuseListPieCharts()
      }, 100) // 延迟确保DOM渲染完成
    })
  }
})

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format('YYYY'), dayjs().format('YYYY')],
    month: [dayjs().format('YYYY-MM'), dayjs().format('YYYY-MM')],
    day: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportReport()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  },
  border: true,
  stripe: true,
  size: 'default',
  tableLayout: 'auto'
})

// 刷新列表数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const type = reportType.find(type => type.value === queryParams.type)
  const date = queryParams[type?.value || 'day']

  if (!date || !date[0] || !date[1]) {
    cardTableConfig.loading = false
    return
  }

  state.title = '水源地取水总量、耗电总量和吨水电耗' + '(' + type?.label + dayjs(date[0]).format(type?.data || 'YYYY-MM-DD') + '至' + dayjs(date[1]).format(type?.data || 'YYYY-MM-DD') + ')'
  const [start, end] = date
  const params = {
    queryType: queryParams.type,
    start: dayjs(start).startOf('day').valueOf(),
    end: dayjs(end).endOf('day').valueOf(),
    name: '' // 不传name参数，获取所有水源地数据
  }



  getWaterSupplyAndEnergyData(params).then(res => {
    const data = res.data?.data || []
    state.dataList = data

    // 设置固定的列结构，符合原型设计
    const columns = [
      { prop: 'index', label: '序号编号', minWidth: 120, align: 'center' },
      { prop: 'name', label: '设备名称', minWidth: 200, align: 'center' },
      { prop: 'unitConsumption', label: '吨水电耗', unit: '(kWh)', minWidth: 180, align: 'center' },
      { prop: 'totalFlow', label: '取水量', unit: '(m³)', minWidth: 180, align: 'center' },
      { prop: 'energy', label: '用电量', unit: '(kW)', minWidth: 180, align: 'center' }
    ]

    cardTableConfig.columns = columns

    // 转换API返回的数据格式为表格需要的格式
    const tableData = data.map((item: any, index: number) => {
      return {
        index: index + 1,
        name: item.name || `水源地${index + 1}`,
        unitConsumption: item.unitConsumption || 0, // 直接使用后端计算的吨水电耗
        totalFlow: item.totalFlow || 0, // 直接使用当期取水量
        energy: item.energy || 0 // 直接使用当期用电量
      }
    })

    cardTableConfig.dataList = tableData
    cardTableConfig.loading = false

    // 根据当前模式刷新对应的图表
    if (state.activeName === 'echarts') {
      nextTick(() => {
        setTimeout(() => {
          refuseChart()
        }, 200) // 延迟确保DOM渲染完成
      })
    } else if (state.activeName === 'list') {
      nextTick(() => {
        setTimeout(() => {
          refuseListPieCharts()
        }, 200) // 延迟确保DOM渲染完成
      })
    }
  }).catch((error) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
    // 显示错误信息
    SLMessage.error('获取数据失败，请稍后重试')
  })
}

// 导出报告
const _exportReport = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 图表大小调整
const resizeChart = () => {
  refChart.value?.resize()
  refPieChart1.value?.resize()
  refPieChart2.value?.resize()
  refPieChart3.value?.resize()
  refListPieChart1.value?.resize()
  refListPieChart2.value?.resize()
  refListPieChart3.value?.resize()
}

// 刷新图表
const refuseChart = () => {
  if (!cardTableConfig.dataList || cardTableConfig.dataList.length === 0) {
    return
  }

  // 检查图表容器是否存在且有尺寸
  const checkAndInitChart = () => {
    const dataList = cardTableConfig.dataList

    // 生成饼图数据
    generatePieCharts(dataList)

    // 生成混合图表
    generateMixedChart(dataList)

    // 延迟调整图表大小
    setTimeout(() => {
      resizeChart()
    }, 100)
  }

  // 清空所有图表
  refChart.value?.clear()
  refPieChart1.value?.clear()
  refPieChart2.value?.clear()
  refPieChart3.value?.clear()

  nextTick(() => {
    checkAndInitChart()
  })
}

// 生成饼图
const generatePieCharts = (dataList: any[]) => {
  const colors = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC']

  // 吨水电耗饼图
  const pieData1 = dataList.map((item: any, index: number) => ({
    name: item.name,
    value: item.unitConsumption || 0,
    itemStyle: { color: colors[index % colors.length] }
  }))

  state.pieChart1Option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} kWh ({d}%)'
    },
    series: [{
      name: '吨水电耗',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: pieData1,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}\n{c} kWh'
      }
    }]
  }

  // 取水量饼图
  const pieData2 = dataList.map((item: any, index: number) => ({
    name: item.name,
    value: item.totalFlow || 0,
    itemStyle: { color: colors[index % colors.length] }
  }))

  state.pieChart2Option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} m³ ({d}%)'
    },
    series: [{
      name: '取水量',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: pieData2,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}\n{c} m³'
      }
    }]
  }

  // 用电量饼图
  const pieData3 = dataList.map((item: any, index: number) => ({
    name: item.name,
    value: item.energy || 0,
    itemStyle: { color: colors[index % colors.length] }
  }))

  state.pieChart3Option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} kW ({d}%)'
    },
    series: [{
      name: '用电量',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: pieData3,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}\n{c} kW'
      }
    }]
  }
}

// 生成混合图表
const generateMixedChart = (dataList: any[]) => {
  const chartOption = barOption()

  // 设置X轴数据（设备名称）
  chartOption.xAxis.data = dataList.map((item: any) => item.name || `设备${item.index}`)

  // 设置图例
  chartOption.legend = {
    top: 20,
    type: 'scroll',
    width: '500',
    textStyle: {
      fontSize: 12
    }
  }

  // 设置Y轴名称
  chartOption.yAxis[0].name = '数值'

  // 生成图表系列数据 - 吨水电耗用折线图，其他用柱状图
  const series = [
    {
      name: '吨水电耗(kWh)',
      type: 'line',
      data: dataList.map((item: any) => item.unitConsumption || 0),
      lineStyle: { color: '#5470C6', width: 3 },
      itemStyle: { color: '#5470C6' },
      symbol: 'circle',
      symbolSize: 6,
      smooth: true,
      yAxisIndex: 0
    },
    {
      name: '取水量(m³)',
      type: 'bar',
      data: dataList.map((item: any) => item.totalFlow || 0),
      itemStyle: { color: '#91CC75' },
      yAxisIndex: 0
    },
    {
      name: '用电量(kW)',
      type: 'bar',
      data: dataList.map((item: any) => item.energy || 0),
      itemStyle: { color: '#FAC858' },
      yAxisIndex: 1 // 使用右侧Y轴，因为用电量数值较大
    }
  ]

  // 添加右侧Y轴用于显示用电量
  chartOption.yAxis.push({
    position: 'right',
    type: 'value',
    name: '用电量(kW)',
    axisLine: {
      show: true,
      lineStyle: {
        color: '#FAC858'
      }
    },
    axisLabel: {
      show: true,
      formatter: '{value}'
    },
    splitLine: {
      show: false
    }
  })

  chartOption.series = series
  state.chartOption = chartOption
}

// 刷新列表模式饼图
const refuseListPieCharts = () => {
  if (!cardTableConfig.dataList || cardTableConfig.dataList.length === 0) {
    return
  }

  // 清空列表模式饼图
  refListPieChart1.value?.clear()
  refListPieChart2.value?.clear()
  refListPieChart3.value?.clear()

  nextTick(() => {
    const dataList = cardTableConfig.dataList

    // 生成列表模式饼图数据
    generateListPieCharts(dataList)

    // 延迟调整图表大小
    setTimeout(() => {
      refListPieChart1.value?.resize()
      refListPieChart2.value?.resize()
      refListPieChart3.value?.resize()
    }, 100)
  })
}

// 生成列表模式饼图
const generateListPieCharts = (dataList: any[]) => {
  const colors = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC']

  // 吨水电耗饼图
  const pieData1 = dataList.map((item: any, index: number) => ({
    name: item.name,
    value: item.unitConsumption || 0,
    itemStyle: { color: colors[index % colors.length] }
  }))

  state.listPieChart1Option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} kWh ({d}%)'
    },
    series: [{
      name: '吨水电耗',
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['50%', '50%'],
      data: pieData1,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}\n{c} kWh',
        fontSize: 11
      }
    }]
  }

  // 取水量饼图
  const pieData2 = dataList.map((item: any, index: number) => ({
    name: item.name,
    value: item.totalFlow || 0,
    itemStyle: { color: colors[index % colors.length] }
  }))

  state.listPieChart2Option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} m³ ({d}%)'
    },
    series: [{
      name: '取水量',
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['50%', '50%'],
      data: pieData2,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}\n{c} m³',
        fontSize: 11
      }
    }]
  }

  // 用电量饼图
  const pieData3 = dataList.map((item: any, index: number) => ({
    name: item.name,
    value: item.energy || 0,
    itemStyle: { color: colors[index % colors.length] }
  }))

  state.listPieChart3Option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} kW ({d}%)'
    },
    series: [{
      name: '用电量',
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['50%', '50%'],
      data: pieData3,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}\n{c} kW',
        fontSize: 11
      }
    }]
  }
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    setTimeout(() => {
      resizeChart()
    }, 100)
  })
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', resizeChart)
})
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 80px);
}

// 列表模式布局
.list-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 254px);
  gap: 20px;
}

.list-pie-charts {
  display: flex;
  gap: 20px;
  height: 280px;
  flex-shrink: 0;
}

.list-pie-item {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.list-pie-chart {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.card-table {
  flex: 1;
  width: 100%;
  min-height: 300px;

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          padding: 16px 12px;
          border-bottom: 2px solid #e9ecef;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }

          td {
            padding: 16px 12px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;

            .cell {
              padding: 0 8px;
              line-height: 1.5;
            }
          }
        }
      }
    }

    // 表格行间距
    .el-table__row {
      height: auto;
    }

    // 表格边框
    &.el-table--border {
      border: 1px solid #ebeef5;

      &::after {
        background-color: #ebeef5;
      }

      &::before {
        background-color: #ebeef5;
      }
    }
  }
}

// 图表容器布局
.chart-container {
  width: 100%;
  height: calc(100vh - 254px);
  min-height: 600px;
  display: flex;
  gap: 20px;
}

.chart-left {
  width: 35%;
  min-width: 300px;
  display: flex;
  flex-direction: column;
}

.chart-right {
  width: 65%;
  min-width: 400px;
  display: flex;
  flex-direction: column;
}

.pie-charts {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.pie-chart-item {
  flex: 1;
  min-height: 180px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.main-chart {
  height: 100%;
  min-height: 500px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.chart-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
  flex-shrink: 0;
}

.pie-chart {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 140px;
}

.mixed-chart {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 420px;
}

// 暗色主题适配
:deep(.el-card.is-dark) {
  .pie-chart-item,
  .main-chart,
  .list-pie-item {
    background: #2d2d2d;

    .chart-title {
      color: #fff;
      border-bottom-color: #404040;
    }
  }
}

// 让表格更宽松美观
:deep(.el-card__body) {
  padding: 20px;
}

// 调整搜索区域的间距
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
