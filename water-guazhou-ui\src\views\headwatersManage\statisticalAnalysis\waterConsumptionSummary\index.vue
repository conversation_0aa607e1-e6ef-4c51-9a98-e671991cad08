<!-- 水源地取水总量、耗电总量和吨水电耗 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'水源地取水总量、耗电总量和吨水电耗':'水源地能耗图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:bar-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        ref="chartContainer"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark?'dark':'light'"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyAndEnergyData, getWaterSupplyDetailReport } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { barOption } from './echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()
const erd = elementResizeDetectorMaker()
const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any,
  title: string,
  activeName: string,
  chartOption: any,
  dataList: any
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  dataList: {}
})

const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()
const chartContainer = ref<any>()

watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      refuseChart()
    })
  }
})

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format('YYYY'), dayjs().format('YYYY')],
    month: [dayjs().format('YYYY-MM'), dayjs().format('YYYY-MM')],
    day: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportReport()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  },
  border: true,
  stripe: true,
  size: 'default',
  tableLayout: 'auto'
})

// 刷新列表数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const type = reportType.find(type => type.value === queryParams.type)
  const date = queryParams[type?.value || 'day']

  if (!date || !date[0] || !date[1]) {
    cardTableConfig.loading = false
    return
  }

  state.title = '水源地取水总量、耗电总量和吨水电耗' + '(' + type?.label + dayjs(date[0]).format(type?.data || 'YYYY-MM-DD') + '至' + dayjs(date[1]).format(type?.data || 'YYYY-MM-DD') + ')'
  const [start, end] = date
  const params = {
    queryType: queryParams.type,
    start: dayjs(start).startOf('day').valueOf(),
    end: dayjs(end).endOf('day').valueOf(),
    name: '' // 不传name参数，获取所有水源地数据
  }

  // 调试信息
  console.log('查询参数:', {
    queryType: params.queryType,
    start: params.start,
    end: params.end,
    startDate: dayjs(params.start).format('YYYY-MM-DD HH:mm:ss'),
    endDate: dayjs(params.end).format('YYYY-MM-DD HH:mm:ss'),
    originalStart: start,
    originalEnd: end
  })

  getWaterSupplyAndEnergyData(params).then(res => {
    const data = res.data?.data || []
    state.dataList = data

    // 设置固定的列结构，符合原型设计
    const columns = [
      { prop: 'index', label: '序号编号', minWidth: 120, align: 'center' },
      { prop: 'name', label: '设备名称', minWidth: 200, align: 'center' },
      { prop: 'unitConsumption', label: '吨水电耗', unit: '(kWh)', minWidth: 180, align: 'center' },
      { prop: 'totalFlow', label: '取水量', unit: '(m³)', minWidth: 180, align: 'center' },
      { prop: 'energy', label: '用电量', unit: '(kW)', minWidth: 180, align: 'center' }
    ]

    cardTableConfig.columns = columns

    // 转换API返回的数据格式为表格需要的格式
    const tableData = data.map((item: any, index: number) => {
      return {
        index: index + 1,
        name: item.name || `水源地${index + 1}`,
        unitConsumption: item.unitConsumption || 0, // 直接使用当期吨水电耗
        totalFlow: item.totalFlow || 0, // 直接使用当期取水量
        energy: item.energy || 0 // 直接使用当期用电量
      }
    })

    cardTableConfig.dataList = tableData
    cardTableConfig.loading = false

    // 如果当前是图表模式，刷新图表
    if (state.activeName === 'echarts') {
      refuseChart()
    }
  }).catch((error) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
    // 显示错误信息
    SLMessage.error('获取数据失败，请稍后重试')
  })
}

// 导出报告
const _exportReport = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 图表大小调整
const resizeChart = () => {
  refChart.value?.resize()
}

// 刷新图表
const refuseChart = () => {
  if (!cardTableConfig.dataList || cardTableConfig.dataList.length === 0) {
    return
  }

  refChart.value?.clear()
  nextTick(() => {
    const chartOption = barOption()
    const dataList = cardTableConfig.dataList

    // 设置X轴数据（设备名称）
    chartOption.xAxis.data = dataList.map((item: any) => item.name || `设备${item.index}`)

    // 设置图例
    chartOption.legend = {
      top: 20,
      type: 'scroll',
      width: '500',
      textStyle: {
        fontSize: 12
      }
    }

    // 设置Y轴名称
    chartOption.yAxis[0].name = '数值'

    // 生成图表系列数据 - 吨水电耗用折线图，其他用柱状图
    const series = [
      {
        name: '吨水电耗(kWh)',
        type: 'line',
        data: dataList.map((item: any) => item.unitConsumption || 0),
        lineStyle: { color: '#5470C6', width: 3 },
        itemStyle: { color: '#5470C6' },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true,
        yAxisIndex: 0
      },
      {
        name: '取水量(m³)',
        type: 'bar',
        data: dataList.map((item: any) => item.totalFlow || 0),
        itemStyle: { color: '#91CC75' },
        yAxisIndex: 0
      },
      {
        name: '用电量(kW)',
        type: 'bar',
        data: dataList.map((item: any) => item.energy || 0),
        itemStyle: { color: '#FAC858' },
        yAxisIndex: 1 // 使用右侧Y轴，因为用电量数值较大
      }
    ]

    // 添加右侧Y轴用于显示用电量
    chartOption.yAxis.push({
      position: 'right',
      type: 'value',
      name: '用电量(kW)',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#FAC858'
        }
      },
      axisLabel: {
        show: true,
        formatter: '{value}'
      },
      splitLine: {
        show: false
      }
    })

    chartOption.series = series
    state.chartOption = chartOption

    // 监听容器大小变化
    if (chartContainer.value) {
      erd.listenTo(chartContainer.value, () => {
        resizeChart()
      })
    }
  })
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()
})
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 80px);
}

.card-table {
  height: calc(100vh - 254px);
  width: 100%;

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          padding: 16px 12px;
          border-bottom: 2px solid #e9ecef;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }

          td {
            padding: 16px 12px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;

            .cell {
              padding: 0 8px;
              line-height: 1.5;
            }
          }
        }
      }
    }

    // 表格行间距
    .el-table__row {
      height: auto;
    }

    // 表格边框
    &.el-table--border {
      border: 1px solid #ebeef5;

      &::after {
        background-color: #ebeef5;
      }

      &::before {
        background-color: #ebeef5;
      }
    }
  }
}

.chart-box {
  width: 100%;
  height: calc(100vh - 254px);
}

// 让表格更宽松美观
:deep(.el-card__body) {
  padding: 20px;
}

// 调整搜索区域的间距
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
