<!-- 水源地取水总量、耗电总量和吨水电耗 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'水源地取水总量、耗电总量和吨水电耗':'水源地能耗图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:bar-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        ref="chartContainer"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark?'dark':'light'"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyConsumptionReport } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { barOption } from './echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'

const { getStationTree } = useStation()
const erd = elementResizeDetectorMaker()
const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any,
  title: string,
  activeName: string,
  chartOption: any,
  dataList: any
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  dataList: {}
})

const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()
const chartContainer = ref<any>()

watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      refuseChart()
    })
  }
})

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format('YYYY'), dayjs().format('YYYY')],
    month: [dayjs().format('YYYY-MM'), dayjs().format('YYYY-MM')],
    day: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportReport()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表数据
const refreshData = () => {
  cardTableConfig.loading = true
  const stationId = TreeData.currentProject.id
  const queryParams = cardSearch.value?.queryParams as any || {}
  const type = reportType.find(type => type.value === queryParams.type)
  const date = queryParams[type?.value || 'day']
  
  if (!date || !date[0] || !date[1]) {
    cardTableConfig.loading = false
    return
  }
  
  state.title = TreeData.currentProject.label + '水源地能耗报表' + '(' + type?.label + dayjs(date[0]).format(type?.data || 'YYYY-MM-DD') + '至' + dayjs(date[1]).format(type?.data || 'YYYY-MM-DD') + ')'
  const [start, end] = date
  const params = {
    stationId,
    queryType: queryParams.type,
    start: dayjs(start).startOf(queryParams.type === 'day' ? 'day' : queryParams.type === 'month' ? 'month' : 'year').valueOf(),
    end: dayjs(end).endOf(queryParams.type === 'day' ? 'day' : queryParams.type === 'month' ? 'month' : 'year').valueOf()
  }
  
  getWaterSupplyConsumptionReport(params).then(res => {
    const data = res.data?.data
    state.dataList = data

    const columns = data?.tableInfo?.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        unit: item.unit ? '(' + (item.unit) + ')' : ''
      }
    })
    cardTableConfig.columns = columns
    cardTableConfig.dataList = data?.tableDataList
    cardTableConfig.loading = false
    
    // 如果当前是图表模式，刷新图表
    if (state.activeName === 'echarts') {
      refuseChart()
    }
  })
}

// 导出报告
const _exportReport = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 图表大小调整
const resizeChart = () => {
  refChart.value?.resize()
}

// 刷新图表
const refuseChart = () => {
  if (!state.dataList?.tableDataList || !state.dataList?.tableInfo) {
    return
  }

  refChart.value?.clear()
  nextTick(() => {
    const chartOption = barOption()
    const tableDataList = state.dataList?.tableDataList
    const tableInfo = state.dataList?.tableInfo
    const queryParams = cardSearch.value?.queryParams as any || {}

    // 过滤掉序号列和非数值列，只显示数值列
    const dataColumns = tableInfo.filter((item: any) =>
      !['序号', '设备名称', '设备厂家', '排序编号'].includes(item.columnName) &&
      item.columnValue !== 'index' &&
      item.columnValue !== 'deviceName' &&
      item.columnValue !== 'manufacturer' &&
      item.columnValue !== 'sortIndex'
    )

    // 使用所有数据行（设备列表）
    const filteredDataList = tableDataList || []

    // 设置X轴数据（设备名称）
    chartOption.xAxis.data = filteredDataList?.map((item: any) => {
      return item.deviceName || item.设备名称 || item.name || `设备${item.index || ''}`
    })

    // 设置图例
    chartOption.legend = {
      top: 20,
      type: 'scroll',
      width: '500',
      textStyle: {
        fontSize: 12
      }
    }

    // 设置Y轴名称
    if (dataColumns.length > 0) {
      chartOption.yAxis[0].name = dataColumns[0].unit ? `${dataColumns[0].columnName}(${dataColumns[0].unit})` : dataColumns[0].columnName
    }

    // 生成柱状图系列数据
    chartOption.series = dataColumns.map((column: any, index: number) => {
      const colors = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC']
      return {
        name: column.columnName,
        type: 'bar',
        data: filteredDataList.map((item: any) => item[column.columnValue]),
        itemStyle: {
          color: colors[index % colors.length]
        },
        label: {
          show: false
        },
        barWidth: dataColumns.length === 1 ? '60%' : undefined, // 单系列时调整柱子宽度
      }
    })

    state.chartOption = chartOption

    // 监听容器大小变化
    if (chartContainer.value) {
      erd.listenTo(chartContainer.value, () => {
        resizeChart()
      })
    }
  })
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()
})
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 80px);
}

.card-table {
  height: calc(100vh - 254px);
  width: 100%;
}

.chart-box {
  width: 100%;
  height: calc(100vh - 254px);
}
</style>
