<!-- 水量报告 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'水质列表':'水质图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:bar-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        ref="chartContainer"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark?'dark':'light'"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyReport } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { barOption } from './echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'

const { getStationTree } = useStation()
const erd = elementResizeDetectorMaker()
const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any,
  title: string,
  activeName: string,
  chartOption: any,
  dataList: any
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  dataList: {}
})

const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()
const chartContainer = ref<any>()

watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      refuseChart()
    })
  }
})

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format('YYYY'), dayjs().format('YYYY')],
    month: [dayjs().format('YYYY-MM'), dayjs().format('YYYY-MM')],
    day: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          // perm: $btnPerms('user_manage_addUser'),
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportWaterQuality()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true
  const stationId = TreeData.currentProject.id
  const queryParams = cardSearch.value?.queryParams as any || {}
  const type = reportType.find(type => type.value === queryParams.type)
  const date = queryParams[type?.value || 'day']

  if (!date || !date[0] || !date[1]) {
    cardTableConfig.loading = false
    return
  }

  state.title = TreeData.currentProject.label + '水量报表' + '(' + type?.label + dayjs(date[0]).format(type?.data || 'YYYY-MM-DD') + '至' + dayjs(date[1]).format(type?.data || 'YYYY-MM-DD') + ')'
  const [start, end] = date
  const params = {
    stationId,
    queryType: queryParams.type,
    start: dayjs(start).startOf(queryParams.type === 'day' ? 'day' : queryParams.type === 'month' ? 'month' : 'year').valueOf(),
    end: dayjs(end).endOf(queryParams.type === 'day' ? 'day' : queryParams.type === 'month' ? 'month' : 'year').valueOf()
  }
  getWaterSupplyReport(params).then(res => {
    const data = res.data?.data
    state.dataList = data

    const columns = data?.tableInfo?.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        unit: item.unit ? '(' + (item.unit) + ')' : ''
      }
    })
    console.log(columns)
    cardTableConfig.columns = columns
    cardTableConfig.dataList = data?.tableDataList
    cardTableConfig.loading = false

    // 如果当前是图表模式，刷新图表
    if (state.activeName === 'echarts') {
      refuseChart()
    }
  })
}
// 导出水量报告
const _exportWaterQuality = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 图表大小调整
const resizeChart = () => {
  refChart.value?.resize()
}

// 刷新图表
const refuseChart = () => {
  if (!state.dataList?.tableDataList || !state.dataList?.tableInfo) {
    return
  }

  refChart.value?.clear()
  nextTick(() => {
    const chartOption = barOption()
    const tableDataList = state.dataList?.tableDataList
    const tableInfo = state.dataList?.tableInfo
    const queryParams = cardSearch.value?.queryParams as any || {}

    // 调试：打印数据结构
    console.log('原始数据:', tableDataList)
    console.log('表头信息:', tableInfo)

    // 过滤掉时间列和统计列，只显示数值列
    const dataColumns = tableInfo.filter((item: any) =>
      !['数据时间', '时间', 'ts', '合计', '总计', '最大值', '最小值', '平均值', '总值'].includes(item.columnName) &&
      item.columnValue !== 'ts' &&
      !item.columnName.includes('最大') &&
      !item.columnName.includes('最小') &&
      !item.columnName.includes('平均') &&
      !item.columnName.includes('总')
    )

    // 智能过滤统计汇总行，保留所有有效的时间数据
    const filteredDataList = tableDataList?.filter((item: any) => {
      const timeValue = item.ts || item.time || item.date
      if (!timeValue) return false

      // 检查是否为统计行（通常统计行的时间字段包含特殊文字）
      const timeStr = String(timeValue)
      const isStatRow = timeStr.includes('合计') ||
                       timeStr.includes('总计') ||
                       timeStr.includes('最大') ||
                       timeStr.includes('最小') ||
                       timeStr.includes('平均') ||
                       timeStr.includes('总值') ||
                       timeStr === '合计' ||
                       timeStr === '总计'

      return !isStatRow
    }) || []

    // 设置X轴数据（时间）- 先直接显示原始值，避免格式化错误
    chartOption.xAxis.data = filteredDataList?.map((item: any) => {
      const timeValue = item.ts || item.time || item.date
      console.log('时间值:', timeValue, '类型:', typeof timeValue)
      return String(timeValue || '')
    })

    // 设置图例
    chartOption.legend = {
      top: 20,
      type: 'scroll',
      width: '500',
      textStyle: {
        fontSize: 12
      }
    }

    // 设置Y轴名称
    if (dataColumns.length > 0) {
      chartOption.yAxis[0].name = dataColumns[0].unit ? `${dataColumns[0].columnName}(${dataColumns[0].unit})` : dataColumns[0].columnName
    }

    // 生成柱状图系列数据
    chartOption.series = dataColumns.map((column: any, index: number) => {
      const colors = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC']
      return {
        name: column.columnName,
        type: 'bar',
        data: filteredDataList.map((item: any) => item[column.columnValue]),
        itemStyle: {
          color: colors[index % colors.length]
        },
        label: {
          show: false
        },
        barWidth: dataColumns.length === 1 ? '60%' : undefined, // 单系列时调整柱子宽度
        // 移除markPoint和markLine，只显示原始数据
      }
    })

    state.chartOption = chartOption

    // 监听容器大小变化
    if (chartContainer.value) {
      erd.listenTo(chartContainer.value, () => {
        resizeChart()
      })
    }
  })
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()
})
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 80px);
}

.card-table {
  height: calc(100vh - 254px);
  width: 100%;
}

.chart-box {
  width: 100%;
  height: calc(100vh - 254px);
}
</style>
