<!-- 单耗报表 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard class="card">
      <div class="card-header">
        <div class="card-title">{{ state.title }}</div>
        <div class="card-tabs">
          <el-radio-group v-model="state.activeName" size="small">
            <el-radio-button label="list">
              <span class="tab-icon">📊</span>
              列表
            </el-radio-button>
            <el-radio-button label="echarts">
              <span class="tab-icon">📈</span>
              图表
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'" class="content-container">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>

      <!-- 图表模式 -->
      <div v-show="state.activeName === 'echarts'" class="content-container">
        <div class="chart-container">
          <div class="chart-header">
            <h4 class="chart-title">单耗趋势图表</h4>
            <el-button size="small" @click="generateLineChart">刷新图表</el-button>
          </div>
          <VChart
            ref="refChart"
            :theme="useAppStore().isDark?'dark':'light'"
            :option="state.chartOption"
            class="line-chart"
          ></VChart>
        </div>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Printer, Search, Grid, TrendCharts } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import {
  getFormatTreeNodeDeepestChild,
  objectLookup
} from '@/utils/GlobalHelper'
import { getWaterSupplyConsumptionReport } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()

const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any;
  title: string;
  activeName: string;
  chartOption: any;
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null
})
const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()

// 监听模式切换
watch(() => state.activeName, () => {
  console.log('模式切换到:', state.activeName)
  if (state.activeName === 'echarts') {
    nextTick(() => {
      setTimeout(() => {
        console.log('开始生成图表...')
        generateLineChart()
      }, 100) // 延迟确保DOM渲染完成
    })
  }
})

// 监听图表配置变化
watch(() => state.chartOption, (newOption) => {
  console.log('图表配置更新:', newOption)
  if (newOption && refChart.value) {
    nextTick(() => {
      setTimeout(() => {
        refChart.value?.resize()
      }, 100)
    })
  }
}, { deep: true })

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

const totalLoading = ref<boolean>(false)
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]

  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      hidden: true,
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        console.log(params)
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    { type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      } },
    { type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      } },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          // perm: $btnPerms('user_manage_addUser'),
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportWaterQuality()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})
// 定义动态表头初始化数据
// let weekDate = reactive<IFormTableColumn[]>([])

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true
  const stationId = TreeData.currentProject.id
  const queryParams = cardSearch.value?.queryParams as any || {}
  const date = queryParams[queryParams.type]
  const type = reportType.find(type => type.value === queryParams.type)
  state.title = TreeData.currentProject.label + '单耗报表' + '(' + type.label + dayjs(date[0]).format(type.data) + '至' + dayjs(date[1]).format(type.data) + ')'
  const params = {
    stationId,
    queryType: queryParams.type,
    start: dayjs(date[0]).startOf(queryParams.type).valueOf(),
    end: dayjs(date[1]).endOf(queryParams.type).valueOf()
  }
  getWaterSupplyConsumptionReport(params).then(res => {
    const data = res.data.data
    const columns = data.tableInfo?.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        unit: item.unit ? '(' + (item.unit) + ')' : ''
      }
    })
    console.log(columns)
    cardTableConfig.columns = columns
    cardTableConfig.dataList = data.tableDataList
    cardTableConfig.loading = false

    // 如果是图表模式，生成图表
    if (state.activeName === 'echarts') {
      nextTick(() => {
        setTimeout(() => {
          generateLineChart()
        }, 200)
      })
    }
  }).catch((error) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
    SLMessage.error('获取数据失败，请稍后重试')
  })
}
// 导出水量报告
const _exportWaterQuality = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 生成折线图
const generateLineChart = () => {
  console.log('开始生成折线图...')
  console.log('数据列表:', cardTableConfig.dataList)
  console.log('列配置:', cardTableConfig.columns)

  if (!cardTableConfig.dataList || cardTableConfig.dataList.length === 0) {
    console.log('数据为空，生成测试图表')
    // 生成一个测试图表
    state.chartOption = {
      title: {
        text: '单耗趋势分析（测试数据）',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        top: 40,
        data: ['单耗', '用水量', '用电量']
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
      },
      yAxis: {
        type: 'value',
        name: '数值'
      },
      series: [
        {
          name: '单耗',
          type: 'line',
          data: [2.5, 2.8, 3.2, 2.9, 2.6, 2.4],
          smooth: true,
          lineStyle: { width: 3, color: '#5470C6' },
          itemStyle: { color: '#5470C6' }
        },
        {
          name: '用水量',
          type: 'line',
          data: [120, 135, 150, 140, 125, 115],
          smooth: true,
          lineStyle: { width: 3, color: '#91CC75' },
          itemStyle: { color: '#91CC75' }
        },
        {
          name: '用电量',
          type: 'line',
          data: [300, 378, 480, 406, 325, 276],
          smooth: true,
          lineStyle: { width: 3, color: '#FAC858' },
          itemStyle: { color: '#FAC858' }
        }
      ]
    }
    return
  }

  const dataList = cardTableConfig.dataList
  const columns = cardTableConfig.columns

  console.log('原始数据长度:', dataList.length)

  // 简化数据处理 - 先不过滤，看看所有数据
  let filteredData = dataList

  // 如果数据中有统计行，尝试过滤
  if (dataList.length > 0) {
    const firstItem = dataList[0]
    const timeFields = Object.keys(firstItem).filter(key =>
      key.includes('time') || key.includes('date') || key === 'ts' || key.includes('Time')
    )

    console.log('找到的时间字段:', timeFields)

    if (timeFields.length > 0) {
      const timeField = timeFields[0]
      filteredData = dataList.filter((item: any) => {
        const timeValue = item[timeField]
        const hasChineseChars = timeValue && /[\u4e00-\u9fa5]/.test(timeValue)
        console.log('时间值:', timeValue, '包含中文:', hasChineseChars)
        return timeValue && !hasChineseChars
      })
    }
  }

  console.log('过滤后数据长度:', filteredData.length)

  if (filteredData.length === 0) {
    console.log('过滤后数据为空')
    // 使用原始数据
    filteredData = dataList.slice(0, Math.min(10, dataList.length)) // 取前10条数据
  }

  // 简化字段识别
  const allKeys = Object.keys(filteredData[0] || {})
  console.log('所有字段:', allKeys)

  // 找时间字段
  const timeField = allKeys.find(key =>
    key.includes('time') || key.includes('date') || key === 'ts' || key.includes('Time')
  ) || allKeys[0] // 如果找不到时间字段，使用第一个字段

  // 找数值字段
  const valueFields = allKeys.filter(key =>
    key !== timeField &&
    !key.includes('name') &&
    !key.includes('index') &&
    !key.includes('序号')
  )

  console.log('时间字段:', timeField)
  console.log('数值字段:', valueFields)

  // 生成X轴数据
  const xAxisData = filteredData.map((item: any, index: number) => {
    const timeValue = item[timeField]
    if (typeof timeValue === 'number') {
      return dayjs(timeValue).format('MM-DD HH:mm')
    }
    return timeValue || `数据${index + 1}`
  })

  console.log('X轴数据:', xAxisData)

  // 生成系列数据
  const colors = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC']

  const series = valueFields.map((field: string, index: number) => {
    const data = filteredData.map((item: any) => {
      const value = item[field]
      return typeof value === 'number' ? value : parseFloat(value) || 0
    })

    console.log(`字段 ${field} 的数据:`, data)

    return {
      name: field,
      type: 'line',
      data: data,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: colors[index % colors.length]
      },
      itemStyle: {
        color: colors[index % colors.length]
      }
    }
  })

  console.log('系列数据:', series)

  // 设置图表配置
  state.chartOption = {
    title: {
      text: '单耗趋势分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      top: 40,
      type: 'scroll',
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLabel: {
        rotate: 45,
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      name: '数值',
      nameTextStyle: {
        fontSize: 12
      },
      axisLabel: {
        fontSize: 11
      }
    },
    series: series
  }

  console.log('最终图表配置:', state.chartOption)

  // 延迟调整图表大小
  nextTick(() => {
    setTimeout(() => {
      console.log('调整图表大小')
      refChart.value?.resize()
    }, 200)
  })
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 100)
  })
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', () => {
    refChart.value?.resize()
  })
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  height: calc(100% - 80px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.card-tabs {
  .tab-icon {
    margin-right: 6px;
    font-size: 16px;
  }
}

.content-container {
  width: 100%;
  height: calc(100vh - 254px);
  min-height: 500px;
}

.chart-container {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.chart-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 12px;
  flex-shrink: 0;
}

.line-chart {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.card-table {
  height: 100%;
  width: 100%;

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          padding: 16px 12px;
          border-bottom: 2px solid #e9ecef;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }

          td {
            padding: 16px 12px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;

            .cell {
              padding: 0 8px;
              line-height: 1.5;
            }
          }
        }
      }
    }

    // 表格行间距
    .el-table__row {
      height: auto;
    }

    // 表格边框
    &.el-table--border {
      border: 1px solid #ebeef5;

      &::after {
        background-color: #ebeef5;
      }

      &::before {
        background-color: #ebeef5;
      }
    }
  }
}

// 暗色主题适配
:deep(.el-card.is-dark) {
  .chart-container {
    background: #2d2d2d;

    .chart-title {
      color: #fff;
      border-bottom-color: #404040;
    }
  }
}

// 让表格更宽松美观
:deep(.el-card__body) {
  padding: 20px;
}
</style>
