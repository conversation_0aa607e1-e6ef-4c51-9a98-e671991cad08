<!-- 单耗报表 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName === 'list' ? '单耗报表' : '单耗趋势图表'"
    >
      <template #right>
        <el-radio-group v-model="state.activeName">
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>

      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'" class="content-container">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>

      <!-- 图表模式 -->
      <div v-show="state.activeName === 'echarts'" class="content-container">
        <div class="chart-layout">
          <!-- 左侧图表区域 -->
          <div class="chart-container">
            <h4 class="chart-title">{{ state.selectedDate }}单耗趋势图表</h4>
            <VChart
              ref="refChart"
              :theme="useAppStore().isDark?'dark':'light'"
              :option="state.chartOption"
              class="line-chart"
            ></VChart>
          </div>

          <!-- 右侧日期选择区域 -->
          <div class="date-selector">
            <div class="selector-title">选择日期</div>
            <div class="date-buttons">
              <el-button
                v-for="date in state.availableDates"
                :key="date"
                :type="state.selectedDate === date ? 'primary' : 'default'"
                size="small"
                @click="selectDate(date)"
                class="date-btn"
              >
                {{ formatDateButton(date) }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import {
  getFormatTreeNodeDeepestChild,
  objectLookup
} from '@/utils/GlobalHelper'
import { getWaterSupplyConsumptionReport } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()

const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any;
  title: string;
  activeName: string;
  chartOption: any;
  availableDates: string[];
  selectedDate: string;
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  availableDates: [],
  selectedDate: ''
})
const today = dayjs().date()

const refTable = ref()
const cardSearch = ref()
const refChart = ref<IECharts>()

// 监听模式切换
watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      setTimeout(() => {
        generateLineChart()
      }, 100) // 延迟确保DOM渲染完成
    })
  }
})

// 监听报表类型变化，重新设置默认选中日期
watch(() => cardSearch.value?.queryParams?.type, (newType) => {
  if (newType && state.availableDates.length > 0) {
    updateDefaultSelectedDate(newType)
  }
})

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})


// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]

  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      hidden: true,
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    { type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      } },
    { type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      } },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportWaterQuality()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})


// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true
  const stationId = TreeData.currentProject.id
  const queryParams = cardSearch.value?.queryParams as any || {}
  const date = queryParams[queryParams.type]
  const type = reportType.find(type => type.value === queryParams.type)
  state.title = TreeData.currentProject.label + '单耗报表' + '(' + type.label + dayjs(date[0]).format(type.data) + '至' + dayjs(date[1]).format(type.data) + ')'
  const params = {
    stationId,
    queryType: queryParams.type,
    start: dayjs(date[0]).startOf(queryParams.type).valueOf(),
    end: dayjs(date[1]).endOf(queryParams.type).valueOf()
  }
  getWaterSupplyConsumptionReport(params).then(res => {
    const data = res.data.data
    const columns = data.tableInfo?.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        unit: item.unit ? '(' + (item.unit) + ')' : ''
      }
    })

    cardTableConfig.columns = columns
    cardTableConfig.dataList = data.tableDataList
    cardTableConfig.loading = false

    // 初始化可用日期列表
    const dateFields = columns.filter((col: any) => {
      const prop = col.prop
      return prop !== 'ts' &&
             prop !== 'min' &&
             prop !== 'max' &&
             prop !== 'avg' &&
             prop !== 'total' &&
             prop.includes('2025-') // 只要日期字段
    })

    state.availableDates = dateFields.map((col: any) => col.prop)

    // 根据报表类型设置默认选中日期
    const currentReportType = queryParams.type || 'day'
    updateDefaultSelectedDate(currentReportType)

    // 如果是图表模式，生成图表
    if (state.activeName === 'echarts') {
      nextTick(() => {
        setTimeout(() => {
          generateLineChart()
        }, 200)
      })
    }
  }).catch((error) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
    SLMessage.error('获取数据失败，请稍后重试')
  })
}
// 导出水量报告
const _exportWaterQuality = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 生成折线图
const generateLineChart = () => {
  if (!cardTableConfig.dataList || cardTableConfig.dataList.length === 0 || !state.selectedDate) {
    return
  }

  const dataList = cardTableConfig.dataList
  const queryParams = cardSearch.value?.queryParams as any || {}
  const reportType = queryParams.type || 'day' // 获取报表类型：day(日报)、month(月报)、year(年报)

  // 根据报表类型过滤数据和生成横坐标
  let filteredData: any[] = []
  let xAxisData: string[] = []
  let xAxisName = ''

  if (reportType === 'day') {
    // 日报：横坐标是小时（00时-23时）
    filteredData = dataList.filter((item: any) => {
      const timeValue = item.ts
      return timeValue && timeValue.includes('时') && !timeValue.includes('最') && !timeValue.includes('平均') && !timeValue.includes('合计')
    })
    xAxisData = filteredData.map((item: any) => item.ts)
    xAxisName = '时间'
  } else if (reportType === 'month') {
    // 月报：横坐标是日（01日-31日）
    filteredData = dataList.filter((item: any) => {
      const timeValue = item.ts
      return timeValue && timeValue.includes('日') && !timeValue.includes('最') && !timeValue.includes('平均') && !timeValue.includes('合计')
    })
    xAxisData = filteredData.map((item: any) => item.ts)
    xAxisName = '日期'
  } else if (reportType === 'year') {
    // 年报：横坐标是月（01月-12月）
    filteredData = dataList.filter((item: any) => {
      const timeValue = item.ts
      return timeValue && timeValue.includes('月') && !timeValue.includes('最') && !timeValue.includes('平均') && !timeValue.includes('合计')
    })
    xAxisData = filteredData.map((item: any) => item.ts)
    xAxisName = '月份'
  }

  if (filteredData.length === 0) {
    return
  }

  // 生成选中日期的数据
  const selectedDateData = filteredData.map((item: any) => {
    const value = item[state.selectedDate]
    return typeof value === 'number' ? value : parseFloat(value) || 0
  })

  // 根据报表类型设置标题
  const titleMap = {
    'day': '日报',
    'month': '月报',
    'year': '年报'
  }
  const reportTypeName = titleMap[reportType as keyof typeof titleMap] || '报表'

  // 设置图表配置
  const chartConfig = {
    title: {
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params: any) {
        const param = params[0]
        return `${param.axisValue}<br/>${param.marker} 单耗: ${param.value} KWh/10³m³`
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      name: xAxisName,
      nameTextStyle: {
        fontSize: 12
      },
      axisLabel: {
        fontSize: 11,
        rotate: reportType === 'day' ? 0 : 45 // 日报不旋转，月报年报旋转45度
      }
    },
    yAxis: {
      type: 'value',
      name: '单耗值 (KWh/10³m³)',
      nameTextStyle: {
        fontSize: 12
      },
      axisLabel: {
        fontSize: 11,
        formatter: '{value}'
      }
    },
    series: [{
      name: '单耗',
      type: 'line',
      data: selectedDateData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#5470C6'
      },
      itemStyle: {
        color: '#5470C6'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(84, 112, 198, 0.3)'
          }, {
            offset: 1, color: 'rgba(84, 112, 198, 0.1)'
          }]
        }
      }
    }]
  }

  state.chartOption = chartConfig

  // 延迟调整图表大小
  nextTick(() => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 200)
  })
}

// 选择日期
const selectDate = (date: string) => {
  state.selectedDate = date
  generateLineChart()
}

// 更新默认选中日期
const updateDefaultSelectedDate = (reportType: string) => {
  if (state.availableDates.length === 0) return

  const queryParams = cardSearch.value?.queryParams as any || {}
  const date = queryParams[reportType]

  if (!date || !date[0]) {
    state.selectedDate = state.availableDates[0]
    return
  }

  let defaultSelectedDate = ''

  if (reportType === 'day') {
    // 日报：选择第一个日期
    defaultSelectedDate = state.availableDates[0]
  } else if (reportType === 'month') {
    // 月报：选择筛选条件范围内的第一个月
    const startDate = dayjs(date[0])
    const targetMonth = startDate.format('YYYY-MM')
    defaultSelectedDate = state.availableDates.find(d => d.startsWith(targetMonth)) || state.availableDates[0]
  } else if (reportType === 'year') {
    // 年报：选择筛选条件范围内的第一个年
    const startDate = dayjs(date[0])
    const targetYear = startDate.format('YYYY')
    defaultSelectedDate = state.availableDates.find(d => d.startsWith(targetYear)) || state.availableDates[0]
  }

  state.selectedDate = defaultSelectedDate

  // 如果当前是图表模式，重新生成图表
  if (state.activeName === 'echarts') {
    nextTick(() => {
      setTimeout(() => {
        generateLineChart()
      }, 100)
    })
  }
}

// 格式化日期按钮显示
const formatDateButton = (date: string) => {
  // 将 2025-06-10 格式转换为 06-10 显示
  const parts = date.split('-')
  if (parts.length === 3) {
    return `${parts[1]}-${parts[2]}`
  }
  return date
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 100)
  })
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', () => {
    refChart.value?.resize()
  })
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  height: calc(100% - 80px);
}



.content-container {
  width: 100%;
  height: calc(100vh - 254px);
  min-height: 500px;
}

.chart-layout {
  display: flex;
  height: 100%;
  gap: 20px;
}

.chart-container {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.date-selector {
  width: 160px;
  min-width: 160px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.selector-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
  flex-shrink: 0;
}

.date-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: stretch;
}

.date-btn {
  width: 100% !important;
  height: 32px !important;
  margin: 0 !important;
  padding: 0 8px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  border-radius: 4px !important;
  font-size: 13px !important;
  box-sizing: border-box !important;
  flex-shrink: 0 !important;
}

.chart-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 12px;
  flex-shrink: 0;
}

.line-chart {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.card-table {
  height: 100%;
  width: 100%;

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          padding: 16px 12px;
          border-bottom: 2px solid #e9ecef;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }

          td {
            padding: 16px 12px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;

            .cell {
              padding: 0 8px;
              line-height: 1.5;
            }
          }
        }
      }
    }

    // 表格行间距
    .el-table__row {
      height: auto;
    }

    // 表格边框
    &.el-table--border {
      border: 1px solid #ebeef5;

      &::after {
        background-color: #ebeef5;
      }

      &::before {
        background-color: #ebeef5;
      }
    }
  }
}

// 暗色主题适配
:deep(.el-card.is-dark) {
  .chart-container {
    background: #2d2d2d;

    .chart-title {
      color: #fff;
      border-bottom-color: #404040;
    }
  }
}

// 让表格更宽松美观
:deep(.el-card__body) {
  padding: 20px;
}

// 强制日期按钮对齐
:deep(.date-buttons .el-button) {
  width: 100% !important;
  margin: 0 !important;
  display: block !important;
}

:deep(.date-buttons .el-button + .el-button) {
  margin-left: 0 !important;
}
</style>
