<!-- 单耗报表 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard class="card">
      <div class="card-header">
        <div class="card-title">{{ state.title }}</div>
        <div class="card-tabs">
          <el-radio-group v-model="state.activeName" size="small">
            <el-radio-button label="list">
              <Icon icon="material-symbols:table-chart" class="tab-icon" />
              列表
            </el-radio-button>
            <el-radio-button label="echarts">
              <Icon icon="material-symbols:analytics" class="tab-icon" />
              图表
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 主容器 - 左右布局 -->
      <div class="main-container">
        <!-- 左侧饼图区域 - 固定显示 -->
        <div class="left-panel">
          <div class="pie-charts">
            <div class="pie-chart-item">
              <h4 class="chart-title">单耗分布</h4>
              <VChart
                ref="refPieChart1"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.pieChart1Option"
                class="pie-chart"
              ></VChart>
            </div>
            <div class="pie-chart-item">
              <h4 class="chart-title">用水量分布</h4>
              <VChart
                ref="refPieChart2"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.pieChart2Option"
                class="pie-chart"
              ></VChart>
            </div>
            <div class="pie-chart-item">
              <h4 class="chart-title">用电量分布</h4>
              <VChart
                ref="refPieChart3"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.pieChart3Option"
                class="pie-chart"
              ></VChart>
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 - 根据模式切换 -->
        <div class="right-panel">
          <!-- 列表模式 -->
          <div v-show="state.activeName === 'list'" class="right-content">
            <CardTable
              id="print"
              ref="refTable"
              class="card-table"
              :config="cardTableConfig"
            />
          </div>

          <!-- 图表模式 -->
          <div v-show="state.activeName === 'echarts'" class="right-content">
            <div class="main-chart">
              <h4 class="chart-title">单耗趋势图表</h4>
              <VChart
                ref="refChart"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.chartOption"
                class="mixed-chart"
              ></VChart>
            </div>
          </div>
        </div>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import {
  getFormatTreeNodeDeepestChild,
  objectLookup
} from '@/utils/GlobalHelper'
import { getWaterSupplyConsumptionReport } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()

const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow:any
  title:string
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: ''
})
const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

const totalLoading = ref<boolean>(false)
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]

  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      hidden: true,
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        console.log(params)
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    { type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      } },
    { type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      } },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          // perm: $btnPerms('user_manage_addUser'),
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportWaterQuality()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})
// 定义动态表头初始化数据
// let weekDate = reactive<IFormTableColumn[]>([])

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true
  const stationId = TreeData.currentProject.id
  const queryParams = cardSearch.value?.queryParams as any || {}
  const date = queryParams[queryParams.type]
  const type = reportType.find(type => type.value === queryParams.type)
  state.title = TreeData.currentProject.label + '单耗报表' + '(' + type.label + dayjs(date[0]).format(type.data) + '至' + dayjs(date[1]).format(type.data) + ')'
  const params = {
    stationId,
    queryType: queryParams.type,
    start: dayjs(date[0]).startOf(queryParams.type).valueOf(),
    end: dayjs(date[1]).endOf(queryParams.type).valueOf()
  }
  getWaterSupplyConsumptionReport(params).then(res => {
    const data = res.data.data
    const columns = data.tableInfo?.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        unit: item.unit ? '(' + (item.unit) + ')' : ''
      }
    })
    console.log(columns)
    cardTableConfig.columns = columns
    cardTableConfig.dataList = data.tableDataList
    cardTableConfig.loading = false
  })
}
// 导出水量报告
const _exportWaterQuality = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()
})
</script>
