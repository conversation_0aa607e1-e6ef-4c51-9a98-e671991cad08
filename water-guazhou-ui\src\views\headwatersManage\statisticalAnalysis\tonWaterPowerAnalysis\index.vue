<!-- 吨水电耗分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'吨水电耗分析':'吨水电耗图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:bar-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <!-- 主容器 - 左右布局 -->
      <div class="main-container">
        <!-- 左侧饼图区域 - 固定显示 -->
        <div class="left-panel">
          <div class="pie-charts">
            <div class="pie-chart-item">
              <h4 class="chart-title">吨水电耗</h4>
              <VChart
                ref="refPieChart1"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.pieChart1Option"
                class="pie-chart"
              ></VChart>
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 - 根据模式切换 -->
        <div class="right-panel">
          <!-- 列表模式 -->
          <div v-show="state.activeName === 'list'" class="right-content">
            <CardTable
              id="print"
              ref="refTable"
              class="card-table"
              :config="cardTableConfig"
            />
          </div>

          <!-- 图表模式 -->
          <div v-show="state.activeName === 'echarts'" class="right-content">
            <div class="main-chart">
              <h4 class="chart-title">吨水电耗对比图表</h4>
              <VChart
                ref="refChart"
                :theme="useAppStore().isDark?'dark':'light'"
                :option="state.chartOption"
                class="mixed-chart"
              ></VChart>
            </div>
          </div>
        </div>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyAndEnergyData } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { barOption } from '@/views/headwatersManage/statisticalAnalysis/waterConsumptionSummary/echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()
const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any,
  title: string,
  activeName: string,
  chartOption: any,
  pieChart1Option: any,
  dataList: any
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  pieChart1Option: null,
  dataList: {}
})

const today = dayjs().date()

const refTable = ref()
const cardSearch = ref()
const refChart = ref<IECharts>()
const refPieChart1 = ref<IECharts>()

watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      setTimeout(() => {
        generateMixedChart(cardTableConfig.dataList)
      }, 100) // 延迟确保DOM渲染完成
    })
  }
  // 饼图是固定显示的，不需要重新生成
})

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format('YYYY'), dayjs().format('YYYY')],
    month: [dayjs().format('YYYY-MM'), dayjs().format('YYYY-MM')],
    day: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportReport()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const date = queryParams[queryParams.type]
  const type = reportType.find(type => type.value === queryParams.type)
  state.title = '吨水电耗分析' + '(' + type.label + dayjs(date[0]).format(type.data) + '至' + dayjs(date[1]).format(type.data) + ')'
  const params = {
    queryType: queryParams.type,
    start: dayjs(date[0]).startOf(queryParams.type).valueOf(),
    end: dayjs(date[1]).endOf(queryParams.type).valueOf()
  }
  getWaterSupplyAndEnergyData(params).then(res => {
    const data = res.data.data
    state.dataList = data

    // 只保留吨水电耗相关的列
    const columns = [
      { prop: 'name', label: '水源地名称' },
      { prop: 'tonWaterPowerConsumption', label: '吨水电耗', unit: '(KWh/10³m³)' }
    ]

    // 只保留吨水电耗数据
    const dataList = data.map((item: any) => ({
      name: item.name,
      tonWaterPowerConsumption: item.tonWaterPowerConsumption || 0
    }))

    cardTableConfig.columns = columns
    cardTableConfig.dataList = dataList
    cardTableConfig.loading = false

    // 生成饼图
    generatePieChart(dataList)

    // 如果是图表模式，生成柱状图
    if (state.activeName === 'echarts') {
      nextTick(() => {
        setTimeout(() => {
          generateMixedChart(dataList)
        }, 200)
      })
    }
  }).catch((error) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
    SLMessage.error('获取数据失败，请稍后重试')
  })
}

// 导出报告
const _exportReport = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 生成饼图
const generatePieChart = (dataList: any[]) => {
  if (!dataList || dataList.length === 0) {
    return
  }

  const pieData = dataList.map(item => ({
    name: item.name,
    value: item.tonWaterPowerConsumption || 0
  }))

  state.pieChart1Option = {
    title: {
      text: '吨水电耗分布',
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} KWh/10³m³ ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '吨水电耗',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: pieData
      }
    ]
  }

  nextTick(() => {
    setTimeout(() => {
      refPieChart1.value?.resize()
    }, 100)
  })
}

// 生成柱状图
const generateMixedChart = (dataList: any[]) => {
  if (!dataList || dataList.length === 0) {
    return
  }

  const names = dataList.map(item => item.name)
  const tonWaterPowerData = dataList.map(item => item.tonWaterPowerConsumption || 0)

  const option = barOption()
  option.xAxis.data = names
  option.series = [
    {
      name: '吨水电耗',
      type: 'bar',
      data: tonWaterPowerData,
      itemStyle: {
        color: '#5470C6'
      },
      label: {
        show: true,
        position: 'top',
        formatter: '{c} KWh/10³m³'
      }
    }
  ]
  option.yAxis[0].name = '吨水电耗 (KWh/10³m³)'

  state.chartOption = option

  nextTick(() => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 200)
  })
}

onMounted(async () => {
  cardSearch.value?.resetForm()
  refreshData()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    setTimeout(() => {
      refChart.value?.resize()
      refPieChart1.value?.resize()
    }, 100)
  })
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', () => {
    refChart.value?.resize()
    refPieChart1.value?.resize()
  })
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  height: calc(100% - 80px);
}

.main-container {
  height: 100%;
  width: 100%;
  display: flex;
  gap: 20px;
}

.left-panel {
  width: 400px;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.right-content {
  height: 100%;
  width: 100%;
}

.pie-charts {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pie-chart-item {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 400px;
  display: flex;
  flex-direction: column;
}

.main-chart {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 12px;
  flex-shrink: 0;
}

.pie-chart {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.mixed-chart {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.card-table {
  height: 100%;
  width: 100%;

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          padding: 16px 12px;
          border-bottom: 2px solid #e9ecef;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }

          td {
            padding: 16px 12px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;

            .cell {
              padding: 0 8px;
              line-height: 1.5;
            }
          }
        }
      }
    }

    // 表格行间距
    .el-table__row {
      height: auto;
    }

    // 表格边框
    &.el-table--border {
      border: 1px solid #ebeef5;

      &::after {
        background-color: #ebeef5;
      }

      &::before {
        background-color: #ebeef5;
      }
    }
  }
}

// 暗色主题适配
:deep(.el-card.is-dark) {
  .pie-chart-item,
  .main-chart {
    background: #2d2d2d;

    .chart-title {
      color: #fff;
      border-bottom-color: #404040;
    }
  }
}

// 让表格更宽松美观
:deep(.el-card__body) {
  padding: 20px;
}
</style>
